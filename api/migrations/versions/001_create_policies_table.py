"""Create policies table

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    op.create_table('policies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('commission_rate', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('travel_start_date', sa.Date(), nullable=True),
        sa.Column('travel_end_date', sa.Date(), nullable=True),
        sa.Column('sale_start_date', sa.Date(), nullable=True),
        sa.Column('sale_end_date', sa.Date(), nullable=True),
        sa.Column('origin', sa.String(length=100), nullable=True),
        sa.Column('destination', sa.String(length=100), nullable=True),
        sa.Column('cabin_classes', sa.String(length=100), nullable=True),
        sa.Column('airline', sa.String(length=100), nullable=True),
        sa.Column('exclusion_rules', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    op.drop_table('policies')