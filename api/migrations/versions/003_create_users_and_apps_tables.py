"""Create users and apps tables

Revision ID: 003
Revises: 002
Create Date: 2025-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None

def upgrade():
    # 创建用户表
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=80), nullable=False),
        sa.Column('email', sa.String(length=120), nullable=False),
        sa.Column('password_hash', sa.String(length=255), nullable=False),
        sa.Column('full_name', sa.String(length=100), nullable=True),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('avatar_url', sa.String(length=255), nullable=True),
        sa.Column('bio', sa.Text(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True),
        sa.Column('is_verified', sa.<PERSON>(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('last_login_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    
    # 创建应用表
    op.create_table('apps',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('slug', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('icon', sa.String(length=255), nullable=True),
        sa.Column('color', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_public', sa.Boolean(), nullable=False, default=False),
        sa.Column('sort_order', sa.Integer(), nullable=False, default=0),
        sa.Column('route_path', sa.String(length=100), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False, default='development'),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index(op.f('ix_apps_slug'), 'apps', ['slug'], unique=True)
    
    # 创建用户应用权限关联表
    op.create_table('user_app_permissions',
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('app_id', sa.Integer(), nullable=False),
        sa.Column('granted_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['app_id'], ['apps.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('user_id', 'app_id')
    )
    
    # 插入默认应用数据
    apps_table = sa.table('apps',
        sa.column('name', sa.String),
        sa.column('slug', sa.String),
        sa.column('description', sa.Text),
        sa.column('icon', sa.String),
        sa.column('color', sa.String),
        sa.column('is_active', sa.Boolean),
        sa.column('is_public', sa.Boolean),
        sa.column('sort_order', sa.Integer),
        sa.column('route_path', sa.String),
        sa.column('status', sa.String),
        sa.column('created_at', sa.DateTime),
        sa.column('updated_at', sa.DateTime)
    )
    
    from datetime import datetime
    now = datetime.utcnow()
    
    op.bulk_insert(apps_table, [
        {
            'name': '智能问数',
            'slug': 'intelligent-qa',
            'description': '基于AI的智能数据查询与分析系统，自然语言交互，快速获取业务洞察',
            'icon': '🧠',
            'color': 'blue',
            'is_active': True,
            'is_public': False,
            'sort_order': 1,
            'route_path': '/intelligent-qa',
            'status': 'development',
            'created_at': now,
            'updated_at': now
        },
        {
            'name': '代理人政策问答',
            'slug': 'agent-policy',
            'description': '代理人政策管理与智能问答系统，支持多模态文件导入和数据分析',
            'icon': '🤝',
            'color': 'green',
            'is_active': True,
            'is_public': False,
            'sort_order': 2,
            'route_path': '/agent-policy',
            'status': 'production',
            'created_at': now,
            'updated_at': now
        },
        {
            'name': '多功能创作引擎',
            'slug': 'content-engine',
            'description': 'AI驱动的内容创作与新闻生成平台，提供智能写作和编辑助手',
            'icon': '✍️',
            'color': 'purple',
            'is_active': True,
            'is_public': False,
            'sort_order': 3,
            'route_path': '/content-engine',
            'status': 'development',
            'created_at': now,
            'updated_at': now
        },
        {
            'name': '智能对账工具',
            'slug': 'reconciliation-tool',
            'description': '自动化财务对账与数据核验系统，大幅提升财务处理效率',
            'icon': '📊',
            'color': 'orange',
            'is_active': True,
            'is_public': False,
            'sort_order': 4,
            'route_path': '/reconciliation-tool',
            'status': 'development',
            'created_at': now,
            'updated_at': now
        }
    ])

def downgrade():
    op.drop_table('user_app_permissions')
    op.drop_index(op.f('ix_apps_slug'), table_name='apps')
    op.drop_table('apps')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')
