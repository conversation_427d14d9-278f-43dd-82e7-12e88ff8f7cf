"""Modify policies table - remove name, add passenger_type, rename exclusion_rules to remark

Revision ID: 002
Revises: 001
Create Date: 2024-01-02 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None

def upgrade():
    # Remove name column
    op.drop_column('policies', 'name')
    
    # Add passenger_type column
    op.add_column('policies', sa.Column('passenger_type', sa.String(length=50), nullable=True))
    
    # Rename exclusion_rules to remark
    op.alter_column('policies', 'exclusion_rules', new_column_name='remark')

def downgrade():
    # Rename remark back to exclusion_rules
    op.alter_column('policies', 'remark', new_column_name='exclusion_rules')
    
    # Remove passenger_type column
    op.drop_column('policies', 'passenger_type')
    
    # Add name column back
    op.add_column('policies', sa.Column('name', sa.String(length=255), nullable=False))