import os
from flask import Blueprint, request, jsonify, send_file
from werkzeug.utils import secure_filename
from services.policy_service import PolicyService

policy_bp = Blueprint('policy', __name__, url_prefix='/api/policies')

# Excel文件扩展名
EXCEL_EXTENSIONS = {'xlsx', 'xls'}

# 多模态文件扩展名
MULTIMODAL_EXTENSIONS = {
    'txt', 'md', 'markdown', 'pdf', 'html', 'xlsx', 'xls', 'docx', 'csv', 'pptx', 'ppt', 'xml', 'epub',  # 文档
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'  # 图片
}

UPLOAD_FOLDER = 'uploads'

def allowed_excel_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in EXCEL_EXTENSIONS

def allowed_multimodal_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in MULTIMODAL_EXTENSIONS

@policy_bp.route('', methods=['GET'])
def get_policies():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        
        result = PolicyService.get_all_policies(page, per_page, search if search else None)
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('', methods=['POST'])
def create_policy():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        policy = PolicyService.create_policy(data)
        return jsonify(policy.to_dict()), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('/<int:policy_id>', methods=['GET'])
def get_policy(policy_id):
    try:
        policy = PolicyService.get_policy_by_id(policy_id)
        if not policy:
            return jsonify({'error': 'Policy not found'}), 404
        
        return jsonify(policy.to_dict()), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('/<int:policy_id>', methods=['PUT'])
def update_policy(policy_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        policy = PolicyService.update_policy(policy_id, data)
        if not policy:
            return jsonify({'error': 'Policy not found'}), 404
        
        return jsonify(policy.to_dict()), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('/<int:policy_id>', methods=['DELETE'])
def delete_policy(policy_id):
    try:
        success = PolicyService.delete_policy(policy_id)
        if not success:
            return jsonify({'error': 'Policy not found'}), 404
        
        return jsonify({'message': 'Policy deleted successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('/import', methods=['POST'])
def import_policies():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_excel_file(file.filename):
            return jsonify({'error': 'Invalid file type. Only .xlsx and .xls files are allowed'}), 400
        
        # Ensure upload directory exists
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        
        imported_policies, errors = PolicyService.import_from_excel(file_path)
        
        # Clean up uploaded file
        os.remove(file_path)
        
        return jsonify({
            'imported_count': len(imported_policies),
            'policies': imported_policies,
            'errors': errors
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('/template', methods=['GET'])
def download_template():
    try:
        template_path = PolicyService.generate_excel_template()
        return send_file(
            template_path,
            as_attachment=True,
            download_name='政策导入模板.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('/extract-multimodal', methods=['POST'])
def extract_from_multimodal():
    """从多模态文件中提取政策数据"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not allowed_multimodal_file(file.filename):
            return jsonify({'error': 'Unsupported file type'}), 400

        # 提取数据
        result = PolicyService.extract_from_multimodal_file(file)

        return jsonify({
            'data': result['data'],
            'validation_errors': result['validation_errors']
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@policy_bp.route('/import-extracted', methods=['POST'])
def import_from_extracted():
    """从提取的数据导入政策"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        imported_policies, errors = PolicyService.import_from_extracted_data(data)

        return jsonify({
            'imported_count': len(imported_policies),
            'policies': imported_policies,
            'errors': errors
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500