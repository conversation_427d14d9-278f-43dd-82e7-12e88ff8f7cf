from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from services.app_service import AppService
from services.email_service import EmailService

apps_bp = Blueprint('apps', __name__, url_prefix='/api/apps')

@apps_bp.route('/', methods=['GET'])
def get_apps():
    """获取应用列表"""
    try:
        # 获取用户可访问的应用
        apps = AppService.get_user_apps()
        
        return jsonify({
            'success': True,
            'apps': apps
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get apps error: {str(e)}")
        return jsonify({'success': False, 'message': '获取应用列表失败'}), 500

@apps_bp.route('/all', methods=['GET'])
@login_required
def get_all_apps():
    """获取所有应用（管理员用）"""
    try:
        # TODO: 添加管理员权限检查
        apps = AppService.get_all_apps()
        
        return jsonify({
            'success': True,
            'apps': apps
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get all apps error: {str(e)}")
        return jsonify({'success': False, 'message': '获取应用列表失败'}), 500

@apps_bp.route('/<slug>', methods=['GET'])
def get_app_by_slug(slug):
    """根据slug获取应用详情"""
    try:
        app = AppService.get_app_by_slug(slug)
        
        if not app:
            return jsonify({'success': False, 'message': '应用不存在'}), 404
        
        # 检查用户是否有权限访问
        if current_user.is_authenticated:
            has_permission = AppService.check_user_app_permission(current_user.id, slug)
        else:
            # 未登录用户只能访问公开应用
            has_permission = app.get('is_public', False)
        
        if not has_permission:
            return jsonify({'success': False, 'message': '无权限访问此应用'}), 403
        
        return jsonify({
            'success': True,
            'app': app,
            'has_permission': has_permission
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get app by slug error: {str(e)}")
        return jsonify({'success': False, 'message': '获取应用详情失败'}), 500

@apps_bp.route('/check-permission/<slug>', methods=['GET'])
@login_required
def check_app_permission(slug):
    """检查用户对应用的权限"""
    try:
        has_permission = AppService.check_user_app_permission(current_user.id, slug)
        
        return jsonify({
            'success': True,
            'has_permission': has_permission
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Check app permission error: {str(e)}")
        return jsonify({'success': False, 'message': '检查权限失败'}), 500

@apps_bp.route('/<int:app_id>/users', methods=['GET'])
@login_required
def get_app_users(app_id):
    """获取有应用权限的用户列表"""
    try:
        # TODO: 添加管理员权限检查
        users = AppService.get_app_users(app_id)
        
        return jsonify({
            'success': True,
            'users': users
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get app users error: {str(e)}")
        return jsonify({'success': False, 'message': '获取用户列表失败'}), 500

@apps_bp.route('/<int:app_id>/grant-permission', methods=['POST'])
@login_required
def grant_app_permission(app_id):
    """授予用户应用权限"""
    try:
        # TODO: 添加管理员权限检查
        data = request.get_json()
        
        if not data or 'user_id' not in data:
            return jsonify({'success': False, 'message': '请提供用户ID'}), 400
        
        user_id = data['user_id']
        success, message = AppService.grant_user_app_permission(user_id, app_id)
        
        if success:
            return jsonify({'success': True, 'message': message}), 200
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Grant app permission error: {str(e)}")
        return jsonify({'success': False, 'message': '权限授予失败'}), 500

@apps_bp.route('/<int:app_id>/revoke-permission', methods=['POST'])
@login_required
def revoke_app_permission(app_id):
    """撤销用户应用权限"""
    try:
        # TODO: 添加管理员权限检查
        data = request.get_json()
        
        if not data or 'user_id' not in data:
            return jsonify({'success': False, 'message': '请提供用户ID'}), 400
        
        user_id = data['user_id']
        success, message = AppService.revoke_user_app_permission(user_id, app_id)
        
        if success:
            return jsonify({'success': True, 'message': message}), 200
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Revoke app permission error: {str(e)}")
        return jsonify({'success': False, 'message': '权限撤销失败'}), 500

@apps_bp.route('/', methods=['POST'])
@login_required
def create_app():
    """创建新应用"""
    try:
        # TODO: 添加管理员权限检查
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供应用信息'}), 400
        
        required_fields = ['name', 'slug']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'{field}不能为空'}), 400
        
        success, message = AppService.create_app(
            name=data['name'],
            slug=data['slug'],
            description=data.get('description'),
            icon=data.get('icon'),
            color=data.get('color'),
            route_path=data.get('route_path'),
            is_public=data.get('is_public', False),
            sort_order=data.get('sort_order', 0)
        )
        
        if success:
            return jsonify({'success': True, 'message': message}), 201
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Create app error: {str(e)}")
        return jsonify({'success': False, 'message': '应用创建失败'}), 500

@apps_bp.route('/<int:app_id>', methods=['PUT'])
@login_required
def update_app(app_id):
    """更新应用信息"""
    try:
        # TODO: 添加管理员权限检查
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供更新信息'}), 400
        
        success, message = AppService.update_app(app_id, **data)
        
        if success:
            return jsonify({'success': True, 'message': message}), 200
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Update app error: {str(e)}")
        return jsonify({'success': False, 'message': '应用更新失败'}), 500

@apps_bp.route('/<int:app_id>', methods=['DELETE'])
@login_required
def delete_app(app_id):
    """删除应用"""
    try:
        # TODO: 添加管理员权限检查
        success, message = AppService.delete_app(app_id)

        if success:
            return jsonify({'success': True, 'message': message}), 200
        else:
            return jsonify({'success': False, 'message': message}), 400

    except Exception as e:
        current_app.logger.error(f"Delete app error: {str(e)}")
        return jsonify({'success': False, 'message': '应用删除失败'}), 500

@apps_bp.route('/trial-application', methods=['POST'])
@login_required
def submit_trial_application():
    """提交试用申请"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'message': '请提供申请信息'}), 400

        # 验证必填字段
        required_fields = ['appSlug', 'appName', 'userEmail', 'userName', 'company', 'phone', 'reason']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'{field}不能为空'}), 400

        # 验证应用是否存在
        app = AppService.get_app_by_slug(data['appSlug'])
        if not app:
            return jsonify({'success': False, 'message': '应用不存在'}), 404

        # 检查用户是否已有权限
        if AppService.check_user_app_permission(current_user.id, data['appSlug']):
            return jsonify({'success': False, 'message': '您已有此应用的使用权限'}), 400

        # 发送试用申请邮件
        success, message = EmailService.send_trial_application_email(
            app_name=data['appName'],
            user_name=data['userName'],
            user_email=data['userEmail'],
            company=data['company'],
            phone=data['phone'],
            reason=data['reason']
        )

        if success:
            return jsonify({'success': True, 'message': '试用申请已提交，我们会尽快处理'}), 200
        else:
            return jsonify({'success': False, 'message': message}), 500

    except Exception as e:
        current_app.logger.error(f"Submit trial application error: {str(e)}")
        return jsonify({'success': False, 'message': '试用申请提交失败'}), 500
