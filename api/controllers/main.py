from flask import Blueprint, jsonify
from controllers.policy import policy_bp

main_bp = Blueprint('main', __name__)

# Register policy blueprint
main_bp.register_blueprint(policy_bp)

@main_bp.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'message': 'API is running'}), 200

@main_bp.route('/api/data', methods=['GET'])
def get_data():
    # Existing data endpoint
    return jsonify({'data': 'sample data'}), 200
