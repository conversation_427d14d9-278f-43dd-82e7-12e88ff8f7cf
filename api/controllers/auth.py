from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from services.auth_service import AuthService

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供注册信息'}), 400
        
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        full_name = data.get('full_name', '').strip()
        
        if not username or not email or not password:
            return jsonify({'success': False, 'message': '用户名、邮箱和密码不能为空'}), 400
        
        success, message = AuthService.register_user(
            username=username,
            email=email,
            password=password,
            full_name=full_name if full_name else None
        )
        
        if success:
            return jsonify({'success': True, 'message': message}), 201
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Register endpoint error: {str(e)}")
        return jsonify({'success': False, 'message': '注册失败，请稍后重试'}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供登录信息'}), 400
        
        username_or_email = data.get('username_or_email', '').strip()
        password = data.get('password', '')
        remember = data.get('remember', False)
        
        if not username_or_email or not password:
            return jsonify({'success': False, 'message': '用户名/邮箱和密码不能为空'}), 400
        
        success, message = AuthService.login_user_by_credentials(
            username_or_email=username_or_email,
            password=password,
            remember=remember
        )
        
        if success:
            user_info = AuthService.get_current_user_info()
            return jsonify({
                'success': True, 
                'message': message,
                'user': user_info
            }), 200
        else:
            return jsonify({'success': False, 'message': message}), 401
            
    except Exception as e:
        current_app.logger.error(f"Login endpoint error: {str(e)}")
        return jsonify({'success': False, 'message': '登录失败，请稍后重试'}), 500

@auth_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """用户登出"""
    try:
        success, message = AuthService.logout_current_user()
        
        if success:
            return jsonify({'success': True, 'message': message}), 200
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Logout endpoint error: {str(e)}")
        return jsonify({'success': False, 'message': '登出失败，请稍后重试'}), 500

@auth_bp.route('/me', methods=['GET'])
@login_required
def get_current_user():
    """获取当前用户信息"""
    try:
        user_info = AuthService.get_current_user_info()
        return jsonify({'success': True, 'user': user_info}), 200
        
    except Exception as e:
        current_app.logger.error(f"Get current user error: {str(e)}")
        return jsonify({'success': False, 'message': '获取用户信息失败'}), 500

@auth_bp.route('/profile', methods=['PUT'])
@login_required
def update_profile():
    """更新用户资料"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供更新信息'}), 400
        
        # 过滤允许更新的字段
        allowed_fields = ['full_name', 'phone', 'bio', 'avatar_url']
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not update_data:
            return jsonify({'success': False, 'message': '没有可更新的字段'}), 400
        
        success, message = AuthService.update_user_profile(
            user_id=current_user.id,
            **update_data
        )
        
        if success:
            user_info = AuthService.get_current_user_info()
            return jsonify({
                'success': True, 
                'message': message,
                'user': user_info
            }), 200
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Update profile error: {str(e)}")
        return jsonify({'success': False, 'message': '更新失败，请稍后重试'}), 500

@auth_bp.route('/change-password', methods=['PUT'])
@login_required
def change_password():
    """修改密码"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供密码信息'}), 400
        
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')
        
        if not old_password or not new_password:
            return jsonify({'success': False, 'message': '原密码和新密码不能为空'}), 400
        
        success, message = AuthService.change_password(
            user_id=current_user.id,
            old_password=old_password,
            new_password=new_password
        )
        
        if success:
            return jsonify({'success': True, 'message': message}), 200
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        current_app.logger.error(f"Change password error: {str(e)}")
        return jsonify({'success': False, 'message': '密码修改失败，请稍后重试'}), 500

@auth_bp.route('/check', methods=['GET'])
def check_auth():
    """检查认证状态"""
    try:
        if current_user.is_authenticated:
            user_info = AuthService.get_current_user_info()
            return jsonify({
                'success': True, 
                'authenticated': True,
                'user': user_info
            }), 200
        else:
            return jsonify({
                'success': True, 
                'authenticated': False,
                'user': None
            }), 200
            
    except Exception as e:
        current_app.logger.error(f"Check auth error: {str(e)}")
        return jsonify({'success': False, 'message': '检查认证状态失败'}), 500
