import os
import json
import re
import requests
import tempfile
import logging
from typing import Dict, Any, List
from werkzeug.datastructures import FileStorage

# 获取logger实例
logger = logging.getLogger(__name__)

class MultiModalExtractor:
    def __init__(self):
        # APS API配置
        self.aps_base_url = os.getenv('APS_BASE_URL', 'http://192.168.1.104/v1')
        self.aps_api_key = os.getenv('APS_API_KEY', '')

    def get_file_type(self, filename: str) -> str:
        """根据文件扩展名确定文件类型"""
        extension = filename.split('.')[-1].lower()

        document_types = ['txt', 'md', 'markdown', 'pdf', 'html', 'xlsx', 'xls', 'docx', 'csv', 'pptx', 'ppt', 'xml', 'epub']
        image_types = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']

        if extension in document_types:
            return 'document'
        elif extension in image_types:
            return 'image'
        else:
            return 'custom'

    def upload_file_to_aps(self, file: FileStorage) -> str:
        """上传文件到APS并获取文件ID"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file.filename.split('.')[-1]}") as temp_file:
                file.save(temp_file.name)
                temp_file_path = temp_file.name

            # 上传文件到APS
            upload_url = f"{self.aps_base_url}/files/upload"

            with open(temp_file_path, 'rb') as f:
                # 构建请求
                files = {
                    'file': (file.filename, f, file.content_type or 'application/octet-stream')
                }
                data = {
                    'user': 'policy_extractor'
                }
                headers = {}
                if self.aps_api_key:
                    headers['Authorization'] = f'Bearer {self.aps_api_key}'

                logger.info(f"上传文件: {file.filename}, Content-Type: {file.content_type}")
                response = requests.post(upload_url, files=files, data=data, headers=headers)
                if response.status_code == 201:  # 201 表示创建成功
                    result = response.json()
                    file_id = result.get('id')
                    logger.info(f"文件上传成功，文件ID: {file_id}")
                    return file_id
                else:
                    raise Exception(f"文件上传失败，状态码: {response.status_code}, 响应: {response.text}")

        except Exception as e:
            raise Exception(f"文件上传失败: {str(e)}")
        finally:
            # 清理临时文件
            if 'temp_file_path' in locals():
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

    def run_workflow(self, file: FileStorage) -> Dict[str, Any]:
        """从文件中提取政策数据"""
        try:
            # 检查文件类型
            file_type = self.get_file_type(file.filename)

            if file_type == 'custom':
                raise Exception(f"不支持的文件类型: {file.filename}")

            # 如果配置了APS，调用workflows/run接口
            if self.aps_base_url:
                try:
                    # 上传文件到APS
                    file_id = self.upload_file_to_aps(file)

                    # 调用workflows/run接口，让APS处理文件解析
                    workflow_url = f"{self.aps_base_url}/workflows/run"
                    headers = {
                        'Content-Type': 'application/json'
                    }
                    if self.aps_api_key:
                        headers['Authorization'] = f'Bearer {self.aps_api_key}'

                    # 根据错误信息修正请求数据格式
                    request_data = {
                        "inputs": {
                            "upload_file": {
                                "transfer_method": "local_file",
                                "upload_file_id": file_id,
                                "type": file_type
                            }
                        },
                        "response_mode": "blocking",
                        "user": "policy_extractor"
                    }

                    logger.info(f"调用APS workflows/run接口: {workflow_url}")
                    logger.debug(f"请求数据: {request_data}")

                    response = requests.post(workflow_url, json=request_data, headers=headers)

                    if response.status_code == 200:
                        result = response.json()
                        logger.info("工作流执行成功")
                        logger.debug(f"APS响应: {result}")
                    else:
                        logger.error(f"工作流执行失败，状态码: {response.status_code}, 响应: {response.text}")
                        raise Exception(f"工作流执行失败，状态码: {response.status_code}, 响应: {response.text}")

                    # 从workflow响应中获取输出数据
                    data = result.get('data', {})
                    outputs = data.get('outputs', {})

                    # 尝试解析workflow响应
                    try:
                        # 检查workflow执行状态
                        status = data.get('status')
                        if status != 'succeeded':
                            error_msg = data.get('error', f'Workflow执行失败，状态: {status}')
                            raise Exception(f"Workflow执行失败: {error_msg}")

                        # 从outputs中获取结果，检查structured_output字段
                        if 'structured_output' in outputs:
                            # 直接返回structured_output中的数据
                            structured_data = outputs['structured_output']
                            logger.info(f"成功获取structured_output数据: {len(structured_data.get('policies', []))} 个政策")
                            return structured_data

                        # 如果没有structured_output，尝试其他字段
                        answer = outputs.get('result', '') or outputs.get('text', '') or outputs.get('output', '')

                        if not answer:
                            # 如果没有找到标准输出字段，尝试获取第一个非空值
                            for key, value in outputs.items():
                                if value and isinstance(value, str):
                                    answer = value
                                    break

                        if not answer:
                            raise ValueError(f"Workflow响应中未找到有效输出，outputs: {outputs}")

                        # 清理响应文本，提取JSON部分
                        # 首先尝试清理可能的编码问题和不可见字符
                        cleaned_answer = answer.strip()

                        # 移除可能的BOM和其他不可见字符
                        if cleaned_answer.startswith('\ufeff'):
                            cleaned_answer = cleaned_answer[1:]

                        # 移除可能的前后空白字符和换行符
                        cleaned_answer = cleaned_answer.strip('\r\n\t ')

                        logger.debug(f"清理后的响应: {repr(cleaned_answer)}")

                        json_start = cleaned_answer.find('{')
                        json_end = cleaned_answer.rfind('}') + 1

                        if json_start >= 0 and json_end > json_start:
                            json_text = cleaned_answer[json_start:json_end]
                            logger.debug(f"提取的JSON文本: {repr(json_text)}")
                            try:
                                extracted_data = json.loads(json_text)
                                return extracted_data
                            except json.JSONDecodeError as e:
                                logger.error(f"JSON解析失败: {str(e)}, JSON文本: {repr(json_text)}")
                                # 尝试更激进的清理
                                # 移除所有控制字符，但保留换行符和制表符
                                clean_json = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', json_text)
                                extracted_data = json.loads(clean_json)
                                return extracted_data
                        else:
                            # 如果没有找到JSON格式，尝试直接解析整个答案
                            try:
                                extracted_data = json.loads(cleaned_answer)
                                return extracted_data
                            except json.JSONDecodeError as e:
                                logger.error(f"直接解析失败: {str(e)}, 响应: {repr(cleaned_answer)}")
                                # 尝试更激进的清理
                                clean_json = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned_answer)
                                extracted_data = json.loads(clean_json)
                                return extracted_data

                    except json.JSONDecodeError as e:
                        raise Exception(f"解析Workflow响应失败: {str(e)}\n原始响应: {repr(answer)}")

                except Exception as aps_error:
                    logger.error(f"APS Workflow API调用失败: {str(aps_error)}")
                    # 如果APS失败，直接抛出异常
                    raise Exception(f"APS服务不可用: {str(aps_error)}")
            else:
                # 如果没有配置APS，抛出异常
                logger.info("未配置APS API")
                raise Exception("未配置APS API")

        except Exception as e:
            # 直接抛出异常，不再使用模拟数据
            logger.error(f"数据提取失败: {str(e)}")
            raise Exception(f"数据提取失败: {str(e)}")

    def validate_extracted_data(self, data: Dict[str, Any]) -> List[str]:
        """验证提取的数据格式"""
        errors = []
        
        if 'policies' not in data:
            errors.append("提取的数据中缺少'policies'字段")
            return errors
            
        if not isinstance(data['policies'], list):
            errors.append("'policies'字段应该是一个数组")
            return errors
            
        for i, policy in enumerate(data['policies']):
            if not isinstance(policy, dict):
                errors.append(f"第{i+1}个政策数据格式错误")
                continue
                
            # 检查必填字段 - 已移除名称字段校验
                
            # 检查佣金费率格式
            commission_rate = policy.get('commission_rate')
            if commission_rate is not None:
                try:
                    float(commission_rate)
                except (ValueError, TypeError):
                    errors.append(f"第{i+1}个政策的佣金费率格式错误")
                    
            # 检查日期格式
            date_fields = ['travel_start_date', 'travel_end_date', 'sale_start_date', 'sale_end_date']
            for field in date_fields:
                date_value = policy.get(field)
                if date_value is not None and date_value != '':
                    try:
                        from datetime import datetime
                        datetime.strptime(str(date_value), '%Y-%m-%d')
                    except ValueError:
                        errors.append(f"第{i+1}个政策的{field}日期格式错误，应为YYYY-MM-DD")
        
        return errors

    def process_file(self, file: FileStorage) -> Dict[str, Any]:
        """处理文件并返回验证后的数据"""
        # 提取数据
        extracted_data = self.run_workflow(file)
        
        # 验证数据
        validation_errors = self.validate_extracted_data(extracted_data)
        
        return {
            'data': extracted_data,
            'validation_errors': validation_errors
        }