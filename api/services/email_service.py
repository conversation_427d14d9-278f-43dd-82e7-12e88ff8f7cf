import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from flask import current_app
import os

class EmailService:
    """邮件服务"""
    
    @staticmethod
    def send_trial_application_email(app_name, user_name, user_email, company, phone, reason):
        """发送试用申请邮件"""
        try:
            # 邮件配置
            smtp_server = os.getenv('SMTP_SERVER', 'smtp.163.com')
            smtp_port = int(os.getenv('SMTP_PORT', 587))
            smtp_username = os.getenv('SMTP_USERNAME', '')
            smtp_password = os.getenv('SMTP_PASSWORD', '')
            
            # 收件人邮箱
            to_email = '<EMAIL>'
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = smtp_username
            msg['To'] = to_email
            msg['Subject'] = f'【试用申请】{app_name} - {user_name}'
            
            # 邮件内容
            body = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #3B82F6; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .info-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .info-table th, .info-table td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
        .info-table th {{ background-color: #f2f2f2; font-weight: bold; }}
        .footer {{ text-align: center; padding: 20px; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>应用试用申请</h1>
        </div>
        
        <div class="content">
            <h2>新的试用申请</h2>
            <p>您收到了一个新的应用试用申请，详情如下：</p>
            
            <table class="info-table">
                <tr>
                    <th>申请应用</th>
                    <td>{app_name}</td>
                </tr>
                <tr>
                    <th>申请人姓名</th>
                    <td>{user_name}</td>
                </tr>
                <tr>
                    <th>申请人邮箱</th>
                    <td>{user_email}</td>
                </tr>
                <tr>
                    <th>公司名称</th>
                    <td>{company}</td>
                </tr>
                <tr>
                    <th>联系电话</th>
                    <td>{phone}</td>
                </tr>
                <tr>
                    <th>申请时间</th>
                    <td>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</td>
                </tr>
            </table>
            
            <h3>申请原因：</h3>
            <div style="background-color: white; padding: 15px; border-left: 4px solid #3B82F6; margin: 10px 0;">
                {reason}
            </div>
            
            <h3>处理建议：</h3>
            <p>请及时处理此试用申请，并通过邮件回复申请人处理结果。</p>
            
            <div style="background-color: #FEF3C7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>注意：</strong>建议在1-2个工作日内处理此申请，以提供良好的用户体验。
            </div>
        </div>
        
        <div class="footer">
            <p>此邮件由XXX管理平台自动发送，请勿直接回复此邮件。</p>
            <p>如有问题，请联系系统管理员。</p>
        </div>
    </div>
</body>
</html>
            """
            
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # 发送邮件
            if smtp_username and smtp_password:
                server = smtplib.SMTP(smtp_server, smtp_port)
                server.starttls()
                server.login(smtp_username, smtp_password)
                server.send_message(msg)
                server.quit()
                
                current_app.logger.info(f"Trial application email sent for {app_name} by {user_name}")
                return True, "试用申请邮件发送成功"
            else:
                # 如果没有配置SMTP，记录到日志
                current_app.logger.info(f"Trial application received for {app_name} by {user_name} ({user_email})")
                current_app.logger.info(f"Company: {company}, Phone: {phone}")
                current_app.logger.info(f"Reason: {reason}")
                return True, "试用申请已记录（邮件服务未配置）"
                
        except Exception as e:
            current_app.logger.error(f"Failed to send trial application email: {str(e)}")
            return False, f"邮件发送失败: {str(e)}"
    
    @staticmethod
    def send_trial_approval_email(user_email, user_name, app_name):
        """发送试用批准邮件给用户"""
        try:
            smtp_server = os.getenv('SMTP_SERVER', 'smtp.163.com')
            smtp_port = int(os.getenv('SMTP_PORT', 587))
            smtp_username = os.getenv('SMTP_USERNAME', '')
            smtp_password = os.getenv('SMTP_PASSWORD', '')
            
            if not smtp_username or not smtp_password:
                return False, "邮件服务未配置"
            
            msg = MIMEMultipart()
            msg['From'] = smtp_username
            msg['To'] = user_email
            msg['Subject'] = f'【试用批准】{app_name} 试用申请已通过'
            
            body = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #10B981; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .success-box {{ background-color: #D1FAE5; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 试用申请已通过</h1>
        </div>
        
        <div class="content">
            <h2>亲爱的 {user_name}，</h2>
            
            <div class="success-box">
                <p><strong>恭喜！</strong>您申请的 <strong>{app_name}</strong> 试用权限已经批准。</p>
            </div>
            
            <p>您现在可以登录系统使用该应用了：</p>
            <ol>
                <li>登录XXX管理平台</li>
                <li>进入应用中心</li>
                <li>找到 {app_name} 应用并开始使用</li>
            </ol>
            
            <p>如果您在使用过程中遇到任何问题，请随时联系我们的技术支持团队。</p>
            
            <p>感谢您对我们产品的关注！</p>
        </div>
        
        <div class="footer">
            <p>此邮件由XXX管理平台自动发送。</p>
            <p>如有问题，请联系技术支持。</p>
        </div>
    </div>
</body>
</html>
            """
            
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()
            
            current_app.logger.info(f"Trial approval email sent to {user_email} for {app_name}")
            return True, "批准邮件发送成功"
            
        except Exception as e:
            current_app.logger.error(f"Failed to send trial approval email: {str(e)}")
            return False, f"邮件发送失败: {str(e)}"
