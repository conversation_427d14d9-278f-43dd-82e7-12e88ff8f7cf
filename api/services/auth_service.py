import re
from datetime import datetime
from flask import current_app
from flask_login import login_user, logout_user, current_user
from models.user import User
from models.app import App
from extensions import db

class AuthService:
    """认证服务"""
    
    @staticmethod
    def validate_email(email):
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_username(username):
        """验证用户名格式"""
        # 用户名只能包含字母、数字、下划线，长度3-20
        pattern = r'^[a-zA-Z0-9_]{3,20}$'
        return re.match(pattern, username) is not None
    
    @staticmethod
    def validate_password(password):
        """验证密码强度"""
        # 密码至少8位，包含字母和数字
        if len(password) < 8:
            return False, "密码长度至少8位"
        
        if not re.search(r'[a-zA-Z]', password):
            return False, "密码必须包含字母"
        
        if not re.search(r'[0-9]', password):
            return False, "密码必须包含数字"
        
        return True, "密码格式正确"
    
    @staticmethod
    def register_user(username, email, password, full_name=None):
        """用户注册"""
        try:
            # 验证输入
            if not AuthService.validate_username(username):
                return False, "用户名格式不正确，只能包含字母、数字、下划线，长度3-20位"
            
            if not AuthService.validate_email(email):
                return False, "邮箱格式不正确"
            
            is_valid, msg = AuthService.validate_password(password)
            if not is_valid:
                return False, msg
            
            # 检查用户名是否已存在
            if User.query.filter_by(username=username).first():
                return False, "用户名已存在"
            
            # 检查邮箱是否已存在
            if User.query.filter_by(email=email).first():
                return False, "邮箱已被注册"
            
            # 创建新用户
            user = User(
                username=username,
                email=email,
                full_name=full_name or username
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            # 为新用户分配默认应用权限（公开应用）
            AuthService.grant_default_apps(user.id)
            
            current_app.logger.info(f"New user registered: {username} ({email})")
            return True, "注册成功"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Registration error: {str(e)}")
            return False, "注册失败，请稍后重试"
    
    @staticmethod
    def login_user_by_credentials(username_or_email, password, remember=False):
        """用户登录"""
        try:
            # 查找用户（支持用户名或邮箱登录）
            user = User.query.filter(
                (User.username == username_or_email) | 
                (User.email == username_or_email)
            ).first()
            
            if not user:
                return False, "用户不存在"
            
            if not user.is_active:
                return False, "账户已被禁用"
            
            if not user.check_password(password):
                return False, "密码错误"
            
            # 登录用户
            login_user(user, remember=remember)
            user.update_last_login()
            
            current_app.logger.info(f"User logged in: {user.username}")
            return True, "登录成功"
            
        except Exception as e:
            current_app.logger.error(f"Login error: {str(e)}")
            return False, "登录失败，请稍后重试"
    
    @staticmethod
    def logout_current_user():
        """用户登出"""
        try:
            if current_user.is_authenticated:
                username = current_user.username
                logout_user()
                current_app.logger.info(f"User logged out: {username}")
                return True, "登出成功"
            return False, "用户未登录"
        except Exception as e:
            current_app.logger.error(f"Logout error: {str(e)}")
            return False, "登出失败"
    
    @staticmethod
    def get_current_user_info():
        """获取当前用户信息"""
        if current_user.is_authenticated:
            return current_user.to_dict()
        return None
    
    @staticmethod
    def update_user_profile(user_id, **kwargs):
        """更新用户资料"""
        try:
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在"
            
            # 允许更新的字段
            allowed_fields = ['full_name', 'phone', 'bio', 'avatar_url']
            
            for field, value in kwargs.items():
                if field in allowed_fields and hasattr(user, field):
                    setattr(user, field, value)
            
            user.updated_at = datetime.utcnow()
            db.session.commit()
            
            return True, "资料更新成功"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Profile update error: {str(e)}")
            return False, "更新失败，请稍后重试"
    
    @staticmethod
    def change_password(user_id, old_password, new_password):
        """修改密码"""
        try:
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在"
            
            if not user.check_password(old_password):
                return False, "原密码错误"
            
            is_valid, msg = AuthService.validate_password(new_password)
            if not is_valid:
                return False, msg
            
            user.set_password(new_password)
            user.updated_at = datetime.utcnow()
            db.session.commit()
            
            return True, "密码修改成功"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Password change error: {str(e)}")
            return False, "密码修改失败，请稍后重试"
    
    @staticmethod
    def grant_default_apps(user_id):
        """为用户分配默认应用权限"""
        try:
            user = User.query.get(user_id)
            if not user:
                return False
            
            # 获取所有公开应用
            public_apps = App.get_public_apps()
            
            # 为用户分配公开应用权限
            for app in public_apps:
                if not user.has_app_permission(app.id):
                    user.grant_app_permission(app.id)
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Grant default apps error: {str(e)}")
            return False
