from flask import current_app
from flask_login import current_user
from models.app import App
from models.user import User
from extensions import db

class AppService:
    """应用服务"""
    
    @staticmethod
    def get_user_apps(user_id=None):
        """获取用户可访问的应用"""
        try:
            if user_id is None and current_user.is_authenticated:
                user_id = current_user.id
            
            if user_id:
                apps = App.get_user_apps(user_id)
            else:
                # 未登录用户只能看到公开应用
                apps = App.get_public_apps()
            
            return [app.to_dict() for app in apps]
            
        except Exception as e:
            current_app.logger.error(f"Get user apps error: {str(e)}")
            return []
    
    @staticmethod
    def get_all_apps():
        """获取所有应用（管理员用）"""
        try:
            apps = App.query.order_by(App.sort_order).all()
            return [app.to_dict() for app in apps]
            
        except Exception as e:
            current_app.logger.error(f"Get all apps error: {str(e)}")
            return []
    
    @staticmethod
    def get_app_by_slug(slug):
        """根据slug获取应用"""
        try:
            app = App.query.filter_by(slug=slug).first()
            return app.to_dict() if app else None
            
        except Exception as e:
            current_app.logger.error(f"Get app by slug error: {str(e)}")
            return None
    
    @staticmethod
    def check_user_app_permission(user_id, app_slug):
        """检查用户是否有应用权限"""
        try:
            user = User.query.get(user_id)
            app = App.query.filter_by(slug=app_slug).first()
            
            if not user or not app:
                return False
            
            # 检查应用是否激活
            if not app.is_active:
                return False
            
            # 公开应用所有人都可以访问
            if app.is_public:
                return True
            
            # 检查用户是否有权限
            return user.has_app_permission(app.id)
            
        except Exception as e:
            current_app.logger.error(f"Check user app permission error: {str(e)}")
            return False
    
    @staticmethod
    def grant_user_app_permission(user_id, app_id):
        """授予用户应用权限"""
        try:
            user = User.query.get(user_id)
            app = App.query.get(app_id)
            
            if not user or not app:
                return False, "用户或应用不存在"
            
            if user.has_app_permission(app_id):
                return False, "用户已有该应用权限"
            
            success = user.grant_app_permission(app_id)
            if success:
                return True, "权限授予成功"
            else:
                return False, "权限授予失败"
                
        except Exception as e:
            current_app.logger.error(f"Grant user app permission error: {str(e)}")
            return False, "权限授予失败，请稍后重试"
    
    @staticmethod
    def revoke_user_app_permission(user_id, app_id):
        """撤销用户应用权限"""
        try:
            user = User.query.get(user_id)
            app = App.query.get(app_id)
            
            if not user or not app:
                return False, "用户或应用不存在"
            
            if not user.has_app_permission(app_id):
                return False, "用户没有该应用权限"
            
            success = user.revoke_app_permission(app_id)
            if success:
                return True, "权限撤销成功"
            else:
                return False, "权限撤销失败"
                
        except Exception as e:
            current_app.logger.error(f"Revoke user app permission error: {str(e)}")
            return False, "权限撤销失败，请稍后重试"
    
    @staticmethod
    def get_app_users(app_id):
        """获取有应用权限的用户列表"""
        try:
            app = App.query.get(app_id)
            if not app:
                return []
            
            users = []
            for user in app.users:
                user_data = user.to_dict()
                # 移除敏感信息
                user_data.pop('password_hash', None)
                users.append(user_data)
            
            return users
            
        except Exception as e:
            current_app.logger.error(f"Get app users error: {str(e)}")
            return []
    
    @staticmethod
    def create_app(name, slug, description=None, icon=None, color=None, 
                   route_path=None, is_public=False, sort_order=0):
        """创建新应用"""
        try:
            # 检查slug是否已存在
            if App.query.filter_by(slug=slug).first():
                return False, "应用标识符已存在"
            
            app = App(
                name=name,
                slug=slug,
                description=description,
                icon=icon,
                color=color,
                route_path=route_path,
                is_public=is_public,
                sort_order=sort_order
            )
            
            db.session.add(app)
            db.session.commit()
            
            return True, "应用创建成功"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Create app error: {str(e)}")
            return False, "应用创建失败，请稍后重试"
    
    @staticmethod
    def update_app(app_id, **kwargs):
        """更新应用信息"""
        try:
            app = App.query.get(app_id)
            if not app:
                return False, "应用不存在"
            
            # 允许更新的字段
            allowed_fields = [
                'name', 'description', 'icon', 'color', 'route_path',
                'is_active', 'is_public', 'sort_order', 'status'
            ]
            
            for field, value in kwargs.items():
                if field in allowed_fields and hasattr(app, field):
                    setattr(app, field, value)
            
            db.session.commit()
            return True, "应用更新成功"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Update app error: {str(e)}")
            return False, "应用更新失败，请稍后重试"
    
    @staticmethod
    def delete_app(app_id):
        """删除应用"""
        try:
            app = App.query.get(app_id)
            if not app:
                return False, "应用不存在"
            
            # 清除所有用户权限关联
            app.users.clear()
            
            db.session.delete(app)
            db.session.commit()
            
            return True, "应用删除成功"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Delete app error: {str(e)}")
            return False, "应用删除失败，请稍后重试"
