from models.data_model import DataModel, Project
from extensions import db

class DataService:
    """数据服务"""
    
    def __init__(self):
        self.data_model = DataModel()
    
    def get_all_data(self):
        """获取所有数据"""
        return self.data_model.get_all_projects()
    
    def create_project(self, name, status='计划中', description=None):
        """创建项目"""
        try:
            project = Project(
                name=name,
                status=status,
                description=description
            )
            db.session.add(project)
            db.session.commit()
            return project.to_dict()
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_project_by_id(self, project_id):
        """根据ID获取项目"""
        project = Project.query.get(project_id)
        return project.to_dict() if project else None
