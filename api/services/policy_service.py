import pandas as pd
import os
import tempfile
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from sqlalchemy import or_
from models.policy import Policy
from extensions import db
from services.multimodal_extractor import MultiModalExtractor

# 获取logger实例
logger = logging.getLogger(__name__)

class PolicyService:
    
    # Column mapping for Excel import
    COLUMN_MAPPING = {
        # 中文列名
        '旅客类型': 'passenger_type',
        '佣金费率': 'commission_rate',
        '承运开始日期': 'travel_start_date',
        '承运结束日期': 'travel_end_date',
        '旅行开始日期': 'travel_start_date',  # 兼容旧列名
        '旅行结束日期': 'travel_end_date',    # 兼容旧列名
        '销售开始日期': 'sale_start_date',
        '销售结束日期': 'sale_end_date',
        '出发地': 'origin',
        '目的地': 'destination',
        '舱位等级': 'cabin_classes',
        '航空公司': 'airline',
        '备注': 'remark',
        # 英文列名（兼容性）
        'passenger_type': 'passenger_type',
        'passenger': 'passenger_type',
        'pax_type': 'passenger_type',
        'commission': 'commission_rate',
        'commission_rate': 'commission_rate',
        'rate': 'commission_rate',
        'travel_start': 'travel_start_date',
        'travel_start_date': 'travel_start_date',
        'travel_end': 'travel_end_date',
        'travel_end_date': 'travel_end_date',
        'sale_start': 'sale_start_date',
        'sale_start_date': 'sale_start_date',
        'sale_end': 'sale_end_date',
        'sale_end_date': 'sale_end_date',
        'from': 'origin',
        'origin': 'origin',
        'to': 'destination',
        'destination': 'destination',
        'cabin': 'cabin_classes',
        'cabin_classes': 'cabin_classes',
        'class': 'cabin_classes',
        'airline': 'airline',
        'carrier': 'airline',
        'remark': 'remark',
        'remarks': 'remark',
        'note': 'remark',
        'notes': 'remark'
    }
    
    @staticmethod
    def get_all_policies(page: int = 1, per_page: int = 20, search: str = None) -> Dict:
        query = Policy.query
        
        if search:
            query = query.filter(
                or_(
                    Policy.passenger_type.ilike(f'%{search}%'),
                    Policy.airline.ilike(f'%{search}%'),
                    Policy.origin.ilike(f'%{search}%'),
                    Policy.destination.ilike(f'%{search}%'),
                    Policy.remark.ilike(f'%{search}%')
                )
            )
        
        policies = query.order_by(Policy.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return {
            'policies': [policy.to_dict() for policy in policies.items],
            'total': policies.total,
            'pages': policies.pages,
            'current_page': page,
            'per_page': per_page
        }
    
    @staticmethod
    def get_policy_by_id(policy_id: int) -> Optional[Policy]:
        return Policy.query.get(policy_id)
    
    @staticmethod
    def create_policy(data: Dict) -> Policy:
        policy = Policy(**PolicyService._validate_policy_data(data))
        db.session.add(policy)
        db.session.commit()
        return policy
    
    @staticmethod
    def update_policy(policy_id: int, data: Dict) -> Optional[Policy]:
        policy = Policy.query.get(policy_id)
        if not policy:
            return None
        
        validated_data = PolicyService._validate_policy_data(data)
        for key, value in validated_data.items():
            setattr(policy, key, value)
        
        policy.updated_at = datetime.utcnow()
        db.session.commit()
        return policy
    
    @staticmethod
    def delete_policy(policy_id: int) -> bool:
        policy = Policy.query.get(policy_id)
        if not policy:
            return False
        
        db.session.delete(policy)
        db.session.commit()
        return True
    
    @staticmethod
    def import_from_excel(file_path: str) -> Tuple[List[Dict], List[str]]:
        try:
            df = pd.read_excel(file_path)
            df.columns = df.columns.str.lower().str.replace(' ', '_')
            
            imported_policies = []
            errors = []
            
            for index, row in df.iterrows():
                try:
                    mapped_data = PolicyService._map_excel_row(row)
                    validated_data = PolicyService._validate_policy_data(mapped_data)

                    policy = Policy(**validated_data)
                    db.session.add(policy)
                    db.session.flush()  # 确保对象有ID
                    imported_policies.append(policy.to_dict())

                except Exception as e:
                    errors.append(f"Row {index + 2}: {str(e)}")
                    db.session.rollback()  # 回滚当前事务
            
            if not errors:
                db.session.commit()
            else:
                db.session.rollback()
            
            return imported_policies, errors
            
        except Exception as e:
            return [], [f"File processing error: {str(e)}"]
    
    @staticmethod
    def _map_excel_row(row: pd.Series) -> Dict:
        mapped_data = {}
        
        for excel_col, model_field in PolicyService.COLUMN_MAPPING.items():
            if excel_col in row.index and pd.notna(row[excel_col]):
                mapped_data[model_field] = row[excel_col]
        
        return mapped_data
    
    @staticmethod
    def _validate_policy_data(data: Dict) -> Dict:
        validated = {}
        
        # Passenger type validation (optional but if provided, must be valid)
        if 'passenger_type' in data and data['passenger_type'] is not None:
            passenger_type = str(data['passenger_type']).strip().upper()
            # Validate passenger types (ADT, CHD, INF)
            valid_types = {'ADT', 'CHD', 'INF'}
            if passenger_type:
                # Support comma-separated values for multiple selection
                types = [t.strip() for t in passenger_type.split(',')]
                invalid_types = [t for t in types if t not in valid_types]
                if invalid_types:
                    raise ValueError(f"Invalid passenger types: {', '.join(invalid_types)}. Valid types are: ADT, CHD, INF")
                validated['passenger_type'] = ','.join(types)
        
        # Optional fields with type conversion
        if 'commission_rate' in data and data['commission_rate'] is not None:
            rate = float(data['commission_rate'])
            if rate > 1:  # Convert percentage to decimal
                rate = rate / 100
            validated['commission_rate'] = rate
        
        # Date fields
        date_fields = ['travel_start_date', 'travel_end_date', 'sale_start_date', 'sale_end_date']
        for field in date_fields:
            if field in data and data[field] is not None:
                try:
                    if isinstance(data[field], str):
                        # 尝试解析字符串日期
                        validated[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
                    elif hasattr(data[field], 'date'):
                        # pandas Timestamp对象
                        validated[field] = data[field].date()
                    elif hasattr(data[field], 'to_pydatetime'):
                        # pandas Timestamp转换为datetime
                        validated[field] = data[field].to_pydatetime().date()
                    else:
                        # 其他类型，尝试直接使用
                        validated[field] = data[field]
                except (ValueError, AttributeError) as e:
                    # 如果日期解析失败，跳过该字段
                    continue
        
        # String fields
        string_fields = ['origin', 'destination', 'cabin_classes', 'airline', 'remark']
        for field in string_fields:
            if field in data:
                if data[field] is not None and data[field] != 'null':
                    # Convert to string and strip whitespace
                    value = str(data[field]).strip()
                    # Only set if not empty after stripping
                    if value:
                        validated[field] = value
                # If None or 'null' or empty string, don't set the field (will be None in DB)
        
        return validated

    @staticmethod
    def generate_excel_template() -> str:
        """生成Excel导入模板文件"""
        # 定义模板列名和示例数据
        template_data = {
            '旅客类型': ['ADT', 'ADT,CHD'],
            '佣金费率': [0.035, 0.025],
            '承运开始日期': ['2025-01-01', '2025-02-01'],
            '承运结束日期': ['2025-12-31', '2025-12-31'],
            '销售开始日期': ['2025-01-01', '2025-01-15'],
            '销售结束日期': ['2025-11-30', '2025-11-30'],
            '出发地': ['深圳', '北京'],
            '目的地': ['洛杉矶', '纽约'],
            '舱位等级': ['Y', 'Y,C'],
            '航空公司': ['深航', '国航'],
            '备注': ['节假日除外', '']
        }

        # 创建DataFrame
        df = pd.DataFrame(template_data)

        # 创建临时文件
        temp_dir = tempfile.gettempdir()
        template_path = os.path.join(temp_dir, '政策导入模板.xlsx')

        # 使用ExcelWriter来设置格式
        with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
            # 写入数据
            df.to_excel(writer, sheet_name='政策数据', index=False)

            # 获取工作表
            worksheet = writer.sheets['政策数据']

            # 设置列宽
            column_widths = {
                'A': 20,  # 旅客类型
                'B': 12,  # 佣金费率
                'C': 15,  # 旅行开始日期
                'D': 15,  # 旅行结束日期
                'E': 15,  # 销售开始日期
                'F': 15,  # 销售结束日期
                'G': 12,  # 出发地
                'H': 12,  # 目的地
                'I': 12,  # 舱位等级
                'J': 12,  # 航空公司
                'K': 20   # 备注
            }

            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width

            # 添加说明工作表
            instructions = pd.DataFrame({
                '字段名称': [
                    '旅客类型', '佣金费率', '承运开始日期', '承运结束日期',
                    '销售开始日期', '销售结束日期', '出发地', '目的地',
                    '舱位等级', '航空公司', '备注'
                ],
                '是否必填': [
                    '否', '否', '否', '否', '否', '否', '否', '否', '否', '否', '否'
                ],
                '格式说明': [
                    '文本，支持多选，用逗号分隔。可选值：ADT（成人）、CHD（儿童）、INF（婴儿）',
                    '小数，如0.035表示3.5%',
                    '日期格式：YYYY-MM-DD',
                    '日期格式：YYYY-MM-DD',
                    '日期格式：YYYY-MM-DD',
                    '日期格式：YYYY-MM-DD',
                    '文本，如：深圳',
                    '文本，如：洛杉矶',
                    '文本，多个用逗号分隔，如：Y,C',
                    '文本，如：深航',
                    '文本，备注信息'
                ],
                '示例': [
                    'ADT,CHD',
                    '0.035',
                    '2025-01-01',
                    '2025-12-31',
                    '2025-01-01',
                    '2025-11-30',
                    '深圳',
                    '洛杉矶',
                    'Y',
                    '深航',
                    '节假日除外'
                ]
            })

            instructions.to_excel(writer, sheet_name='填写说明', index=False)

            # 设置说明工作表的列宽
            instructions_sheet = writer.sheets['填写说明']
            instructions_sheet.column_dimensions['A'].width = 15
            instructions_sheet.column_dimensions['B'].width = 10
            instructions_sheet.column_dimensions['C'].width = 25
            instructions_sheet.column_dimensions['D'].width = 20

        return template_path

    @staticmethod
    def extract_from_multimodal_file(file) -> Dict:
        """从多模态文件中提取政策数据"""
        try:
            extractor = MultiModalExtractor()
            result = extractor.process_file(file)
            return result
        except Exception as e:
            raise Exception(f"多模态文件处理失败: {str(e)}")

    @staticmethod
    def import_from_extracted_data(extracted_data: Dict) -> Tuple[List[Dict], List[str]]:
        """从提取的数据导入政策"""
        try:
            logger.info(f"开始导入提取的数据: {extracted_data}")

            # 检查数据结构，支持两种格式：
            # 1. {'policies': [...]}  - 直接格式
            # 2. {'data': {'policies': [...]}}  - 嵌套格式
            if 'data' in extracted_data and 'policies' in extracted_data['data']:
                policies_data = extracted_data['data']['policies']
            elif 'policies' in extracted_data:
                policies_data = extracted_data['policies']
            else:
                raise ValueError("提取的数据中缺少policies字段")
            imported_policies = []
            errors = []

            logger.info(f"准备导入 {len(policies_data)} 个政策")

            for index, policy_data in enumerate(policies_data):
                try:
                    logger.debug(f"处理政策 {index + 1}: {policy_data}")

                    # 验证和清理数据
                    validated_data = PolicyService._validate_policy_data(policy_data)
                    logger.debug(f"验证后的数据: {validated_data}")

                    # 创建政策对象
                    policy = Policy(**validated_data)
                    db.session.add(policy)
                    db.session.flush()  # 确保对象有ID
                    imported_policies.append(policy.to_dict())

                    logger.info(f"成功处理政策 {index + 1}")

                except Exception as e:
                    error_msg = f"政策 {index + 1}: {str(e)}"
                    logger.error(f"导入政策失败: {error_msg}")
                    errors.append(error_msg)
                    db.session.rollback()  # 回滚当前事务

            if not errors:
                db.session.commit()
                logger.info(f"成功导入 {len(imported_policies)} 个政策")
            else:
                db.session.rollback()
                logger.warning(f"导入完成，但有 {len(errors)} 个错误")

            return imported_policies, errors

        except Exception as e:
            logger.error(f"导入提取数据失败: {str(e)}")
            db.session.rollback()
            raise Exception(f"导入提取数据失败: {str(e)}")