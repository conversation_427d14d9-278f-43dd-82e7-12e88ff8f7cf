from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.orm import relationship
from extensions import db
from models.user import user_app_permissions

class App(db.Model):
    """应用模型"""
    __tablename__ = 'apps'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    slug = Column(String(50), unique=True, nullable=False, index=True)  # URL友好的标识符
    description = Column(Text, nullable=True)
    icon = Column(String(255), nullable=True)  # 图标URL或图标类名
    color = Column(String(20), nullable=True)  # 主题颜色
    
    # 应用配置
    is_active = Column(Boolean, default=True, nullable=False)
    is_public = Column(Boolean, default=False, nullable=False)  # 是否公开应用
    sort_order = Column(Integer, default=0, nullable=False)  # 排序顺序
    
    # 应用路径和状态
    route_path = Column(String(100), nullable=True)  # 应用路由路径
    status = Column(String(20), default='development', nullable=False)  # development, production, maintenance
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 关联关系
    users = relationship('User', secondary=user_app_permissions, back_populates='apps')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'icon': self.icon,
            'color': self.color,
            'is_active': self.is_active,
            'is_public': self.is_public,
            'sort_order': self.sort_order,
            'route_path': self.route_path,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'user_count': len(self.users) if hasattr(self, 'users') else 0
        }
    
    @classmethod
    def get_public_apps(cls):
        """获取公开应用"""
        return cls.query.filter_by(is_public=True, is_active=True).order_by(cls.sort_order).all()
    
    @classmethod
    def get_user_apps(cls, user_id):
        """获取用户有权限的应用"""
        from models.user import User
        user = User.query.get(user_id)
        if not user:
            return []
        
        # 获取用户有权限的应用 + 公开应用
        user_apps = [app for app in user.apps if app.is_active]
        public_apps = cls.get_public_apps()
        
        # 合并并去重
        all_apps = {}
        for app in user_apps + public_apps:
            all_apps[app.id] = app
        
        # 按排序顺序返回
        return sorted(all_apps.values(), key=lambda x: x.sort_order)
