from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Numeric
from extensions import db

class Policy(db.Model):
    __tablename__ = 'policies'
    
    id = Column(Integer, primary_key=True)
    passenger_type = Column(String(50), nullable=True)  # ADT,CHD,INF - comma separated for multiple selection
    commission_rate = Column(Numeric(5, 4), nullable=True)  # e.g., 0.0350 for 3.5%
    travel_start_date = Column(Date, nullable=True)
    travel_end_date = Column(Date, nullable=True)
    sale_start_date = Column(Date, nullable=True)
    sale_end_date = Column(Date, nullable=True)
    origin = Column(String(100), nullable=True)
    destination = Column(String(100), nullable=True)
    cabin_classes = Column(String(100), nullable=True)
    airline = Column(String(100), nullable=True)
    remark = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        def safe_isoformat(date_obj):
            """安全地转换日期为ISO格式字符串"""
            if date_obj is None:
                return None
            try:
                return date_obj.isoformat()
            except (AttributeError, TypeError):
                return None

        return {
            'id': self.id,
            'passenger_type': self.passenger_type,
            'commission_rate': float(self.commission_rate) if self.commission_rate else None,
            'travel_start_date': safe_isoformat(self.travel_start_date),
            'travel_end_date': safe_isoformat(self.travel_end_date),
            'sale_start_date': safe_isoformat(self.sale_start_date),
            'sale_end_date': safe_isoformat(self.sale_end_date),
            'origin': self.origin,
            'destination': self.destination,
            'cabin_classes': self.cabin_classes,
            'airline': self.airline,
            'remark': self.remark,
            'created_at': safe_isoformat(self.created_at),
            'updated_at': safe_isoformat(self.updated_at)
        }