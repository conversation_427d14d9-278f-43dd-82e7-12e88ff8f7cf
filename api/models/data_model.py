from datetime import datetime
from extensions import db

class Project(db.Model):
    """项目模型"""
    __tablename__ = 'projects'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='计划中')
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'status': self.status,
            'description': self.description,
            'created_at': self.created_at.strftime('%Y-%m-%d'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d')
        }

class DataModel:
    """数据访问层"""
    
    def get_mock_data(self):
        """Mock数据库查询"""
        return [
            {'id': 1, 'name': '项目A', 'status': '进行中', 'created_at': '2024-01-01'},
            {'id': 2, 'name': '项目B', 'status': '已完成', 'created_at': '2024-01-02'},
            {'id': 3, 'name': '项目C', 'status': '计划中', 'created_at': '2024-01-03'},
        ]
    
    def get_all_projects(self):
        """获取所有项目"""
        try:
            projects = Project.query.all()
            return [project.to_dict() for project in projects]
        except Exception:
            # 如果数据库查询失败，返回 Mock 数据
            return self.get_mock_data()
