from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Table, ForeignKey
from sqlalchemy.orm import relationship
from extensions import db

# 用户应用权限关联表
user_app_permissions = Table(
    'user_app_permissions',
    db.Model.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('app_id', Integer, ForeignKey('apps.id'), primary_key=True),
    Column('granted_at', DateTime, default=datetime.utcnow)
)

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # 用户基本信息
    full_name = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    bio = Column(Text, nullable=True)
    
    # 账户状态
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login_at = Column(DateTime, nullable=True)
    
    # 关联关系
    apps = relationship('App', secondary=user_app_permissions, back_populates='users')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login_at = datetime.utcnow()
        db.session.commit()
    
    def has_app_permission(self, app_id):
        """检查用户是否有应用权限"""
        return any(app.id == app_id for app in self.apps)
    
    def grant_app_permission(self, app_id):
        """授予应用权限"""
        from models.app import App
        app = App.query.get(app_id)
        if app and not self.has_app_permission(app_id):
            self.apps.append(app)
            db.session.commit()
            return True
        return False
    
    def revoke_app_permission(self, app_id):
        """撤销应用权限"""
        from models.app import App
        app = App.query.get(app_id)
        if app and self.has_app_permission(app_id):
            self.apps.remove(app)
            db.session.commit()
            return True
        return False
    
    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'phone': self.phone,
            'avatar_url': self.avatar_url,
            'bio': self.bio,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None,
            'apps': [app.to_dict() for app in self.apps] if hasattr(self, 'apps') else []
        }
        
        if include_sensitive:
            data['password_hash'] = self.password_hash
            
        return data
