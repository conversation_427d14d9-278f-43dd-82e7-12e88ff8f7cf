# Flask 配置
FLASK_APP=app.py
FLASK_ENV=development
FLASK_DEBUG=true
SECRET_KEY=your-secret-key-here

# 服务器配置
API_HOST=0.0.0.0
API_PORT=5000

# 数据库配置
DB_USERNAME=postgres
DB_PASSWORD=your-db-password
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=scaffold_db
DATABASE_URL=postgresql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}

# 日志配置 Supported values are `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`
LOG_LEVEL=DEBUG
LOG_FILE=logs/app.log

# CORS 配置
CORS_ORIGINS=http://localhost:4000,http://127.0.0.1:4000

# 应用配置
APP_NAME=HOWPOT API
APP_VERSION=0.1.0

# APS API 配置
APS_BASE_URL=http://*************/v1
APS_API_KEY=app-bvejDGPH3p4IJegJ1DGspzIc
