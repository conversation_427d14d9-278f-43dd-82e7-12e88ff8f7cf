#!/bin/bash

# 设置 fnm 环境变量
export FNM_PATH="/home/<USER>/.local/share/fnm"
export FNM_ARCH="x64"
export FNM_NODE_DIST_MIRROR="https://nodejs.org/dist"
export FNM_COREPACK_ENABLED="false"
export FNM_VERSION_FILE_STRATEGY="local"
export FNM_RESOLVE_ENGINES="true"
export FNM_DIR="/home/<USER>/.local/share/fnm"
export FNM_LOGLEVEL="info"

# 确保 fnm 在 PATH 中
if [ -d "$FNM_PATH" ]; then
  export PATH="$FNM_PATH:$PATH"
fi

# 加载 fnm 环境
if command -v fnm >/dev/null 2>&1; then
  eval "$(fnm env --use-on-cd)"
fi

# 执行传入的命令
exec "$@"
