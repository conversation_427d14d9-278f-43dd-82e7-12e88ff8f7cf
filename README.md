# Minimal Scaffold Project

脚手架工程

## 项目结构

```
project/
├── api/                          # Flask 后端 API
│   ├── controllers/              # API 控制器层
│   │   └── main.py              # 主要路由控制器
│   ├── services/                # 业务逻辑层
│   │   └── data_service.py      # 数据服务
│   ├── models/                  # 数据模型层
│   │   └── data_model.py        # 数据模型定义
│   ├── core/                    # 核心功能模块
│   ├── libs/                    # 工具库
│   ├── migrations/              # 数据库迁移文件
│   ├── tests/                   # 测试文件
│   ├── logs/                    # 日志目录
│   ├── extensions.py            # Flask 扩展初始化
│   ├── config.py                # 配置文件
│   ├── app.py                   # Flask 应用入口
│   ├── pyproject.toml           # Python 依赖管理 (uv)
│   └── .env.example             # 环境变量模板
├── web/                         # Next.js 前端应用
│   ├── app/                     # Next.js App Router
│   │   ├── globals.css          # 全局样式
│   │   ├── layout.tsx           # 根布局组件
│   │   └── page.tsx             # 首页组件
│   ├── node_modules/            # Node.js 依赖
│   ├── .next/                   # Next.js 构建输出
│   ├── package.json             # Node.js 依赖配置
│   ├── next.config.ts           # Next.js 配置
│   ├── tailwind.config.js       # Tailwind CSS 配置
│   ├── tsconfig.json            # TypeScript 配置
│   ├── .env.local               # 本地环境变量
│   └── .env.example             # 环境变量模板
├── docker-compose.yml           # Docker 容器编排
├── create_project.py            # 项目创建脚本
├── start.sh                     # Linux/Mac 启动脚本
└── README.md                    # 项目说明文档
```

## 技术栈

### 后端 (api/)
- **框架**: Flask 3.x
- **数据库**: SQLAlchemy ORM
- **包管理**: uv (Python 包管理器)
- **架构**: MVC 分层架构
  - Controllers: 处理 HTTP 请求和响应
  - Services: 业务逻辑处理
  - Models: 数据模型和数据库操作

### 前端 (web/)
- **框架**: Next.js 15.x (App Router)
- **UI**: React 19.x + TypeScript
- **样式**: Tailwind CSS 4.x
- **构建**: Turbopack (开发) + Webpack (生产)
- **包管理**: pnpm

### 开发工具
- **容器化**: Docker + Docker Compose
- **代码规范**: ESLint + TypeScript
- **环境管理**: .env 文件

## 依赖项

- Docker & Docker Compose
- Node.js 18+
- Python 3.10+
- uv (Python 包管理器)

## 安装

### 后端
```bash
cd api/
# 复制环境配置
cp .env.example .env
# 安装依赖
uv sync
# 运行应用
uv run flask run --host 0.0.0.0 --port=5000 --debug
```

### 前端
```bash
cd web/
cp .env.example .env.local
pnpm install
pnpm run dev
```

## 访问

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## API 接口

后端 API 服务运行在 [http://localhost:5000](http://localhost:5000)

### 主要接口
- `GET /api/data` - 获取数据列表
- `POST /api/data` - 创建新数据
- `GET /api/health` - 健康检查

## 开发说明

### 目录说明
- **api/controllers/**: API 路由和请求处理
- **api/services/**: 业务逻辑，连接控制器和模型
- **api/models/**: 数据模型，数据库操作
- **api/extensions.py**: Flask 扩展初始化，避免循环导入
- **web/app/**: Next.js 页面和组件
- **web/.env.local**: 前端环境变量（不提交到版本控制）

### 开发流程
1. 后端开发：修改 controllers → services → models
2. 前端开发：修改 app/ 目录下的页面和组件
3. API 调用：前端通过 `/api/*` 路径代理到后端

### 环境变量
- 后端：复制 `api/.env.example` 到 `api/.env`
- 前端：复制 `web/.env.example` 到 `web/.env.local`
