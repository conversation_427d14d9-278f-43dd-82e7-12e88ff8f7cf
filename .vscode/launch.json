{"version": "0.2.0", "compounds": [{"name": "Launch Flask and Next", "configurations": ["Python: Flask", "Next: <PERSON> (Task)"]}], "configurations": [{"name": "Python: Flask", "consoleName": "Python: Flask", "type": "debugpy", "request": "launch", "python": "${workspaceFolder}/api/.venv/bin/python", "cwd": "${workspaceFolder}/api", "envFile": "${workspaceFolder}/api/.env", "module": "flask", "justMyCode": true, "jinja": true, "env": {"FLASK_APP": "app.py", "FLASK_DEBUG": "1"}, "args": ["run", "--port=5000", "--reload"]}, {"name": "Next: <PERSON>", "type": "node", "request": "launch", "preLaunchTask": "Next: <PERSON>", "program": "${workspaceFolder}/web/node_modules/.bin/next", "args": ["dev"], "cwd": "${workspaceFolder}/web", "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal"}], "extensions": {"recommendations": ["ms-python.python", "ms-python.debugpy", "golang.go", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "vue.volar", "vue.vscode-typescript-vue-plugin", "*"], "unwantedRecommendations": []}}