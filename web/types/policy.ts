export interface Policy {
  id: number;
  passenger_type?: string;
  commission_rate?: number;
  travel_start_date?: string;
  travel_end_date?: string;
  sale_start_date?: string;
  sale_end_date?: string;
  origin?: string;
  destination?: string;
  cabin_classes?: string;
  airline?: string;
  remark?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PolicyFormData {
  passenger_type?: string;
  commission_rate?: number;
  travel_start_date?: string;
  travel_end_date?: string;
  sale_start_date?: string;
  sale_end_date?: string;
  origin?: string;
  destination?: string;
  cabin_classes?: string;
  airline?: string;
  remark?: string;
}

export interface PolicyListResponse {
  policies: Policy[];
  total: number;
  pages: number;
  current_page: number;
  per_page: number;
}

export interface ImportResult {
  imported_count: number;
  policies: Policy[];
  errors: string[];
}
