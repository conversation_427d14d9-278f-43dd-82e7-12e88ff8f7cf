export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  phone?: string;
  avatar_url?: string;
  bio?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
  apps: App[];
}

export interface App {
  id: number;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  is_active: boolean;
  is_public: boolean;
  sort_order: number;
  route_path?: string;
  status: 'development' | 'production' | 'maintenance';
  created_at: string;
  updated_at: string;
  user_count: number;
}

export interface LoginRequest {
  username_or_email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface UpdateProfileRequest {
  full_name?: string;
  phone?: string;
  bio?: string;
  avatar_url?: string;
}

export interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
}

export interface AuthState {
  user: User | null;
  authenticated: boolean;
  loading: boolean;
}

export interface AppsResponse {
  success: boolean;
  apps: App[];
  message?: string;
}

export interface AppPermissionResponse {
  success: boolean;
  has_permission: boolean;
  message?: string;
}
