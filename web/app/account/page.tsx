'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/service/auth-context';
import { apiClient } from '@/service/api';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AccountPage() {
  const router = useRouter();
  const { user, authenticated, loading, updateUser } = useAuth();
  
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setSaving] = useState(false);
  
  const [profileData, setProfileData] = useState({
    full_name: '',
    phone: '',
    bio: '',
    avatar_url: '',
  });
  
  const [passwordData, setPasswordData] = useState({
    old_password: '',
    new_password: '',
    confirm_password: '',
  });
  
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [successMessage, setSuccessMessage] = useState('');

  // 检查登录状态
  useEffect(() => {
    if (!loading && !authenticated) {
      router.push('/auth/login');
    }
  }, [authenticated, loading, router]);

  // 初始化用户数据
  useEffect(() => {
    if (user) {
      setProfileData({
        full_name: user.full_name || '',
        phone: user.phone || '',
        bio: user.bio || '',
        avatar_url: user.avatar_url || '',
      });
    }
  }, [user]);

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // 清除错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // 清除错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSaveProfile = async () => {
    setSaving(true);
    setErrors({});
    setSuccessMessage('');
    
    try {
      const result = await apiClient.updateProfile(profileData);
      
      if (result.success && result.user) {
        updateUser(result.user);
        setSuccessMessage('资料更新成功');
        setIsEditing(false);
      } else {
        setErrors({ general: result.message });
      }
    } catch (error) {
      setErrors({ general: '更新失败，请稍后重试' });
    } finally {
      setSaving(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证密码
    const newErrors: { [key: string]: string } = {};
    
    if (!passwordData.old_password) {
      newErrors.old_password = '请输入原密码';
    }
    
    if (!passwordData.new_password) {
      newErrors.new_password = '请输入新密码';
    } else if (passwordData.new_password.length < 8) {
      newErrors.new_password = '密码长度至少8位';
    }
    
    if (passwordData.new_password !== passwordData.confirm_password) {
      newErrors.confirm_password = '两次输入的密码不一致';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setSaving(true);
    setErrors({});
    setSuccessMessage('');
    
    try {
      const result = await apiClient.changePassword({
        old_password: passwordData.old_password,
        new_password: passwordData.new_password,
      });
      
      if (result.success) {
        setSuccessMessage('密码修改成功');
        setPasswordData({
          old_password: '',
          new_password: '',
          confirm_password: '',
        });
      } else {
        setErrors({ password_general: result.message });
      }
    } catch (error) {
      setErrors({ password_general: '密码修改失败，请稍后重试' });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="账户设置"
      />

      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">账户设置</h1>
          <p className="mt-2 text-gray-600">管理您的个人信息和账户安全</p>
        </div>

        {/* 成功消息 */}
        {successMessage && (
          <div className="mb-6 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md">
            {successMessage}
          </div>
        )}

        <div className="bg-white shadow rounded-lg">
          {/* 标签页 */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'profile'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                个人资料
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'security'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                安全设置
              </button>
            </nav>
          </div>

          {/* 个人资料标签页 */}
          {activeTab === 'profile' && (
            <div className="p-6">
              {errors.general && (
                <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                  {errors.general}
                </div>
              )}

              <div className="space-y-6">
                {/* 基本信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        用户名
                      </label>
                      <input
                        type="text"
                        value={user.username}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱
                      </label>
                      <input
                        type="email"
                        value={user.email}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        姓名
                      </label>
                      <input
                        type="text"
                        name="full_name"
                        value={profileData.full_name}
                        onChange={handleProfileChange}
                        disabled={!isEditing}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md ${
                          isEditing ? 'bg-white' : 'bg-gray-50 text-gray-500'
                        }`}
                        placeholder="请输入姓名"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        手机号
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={profileData.phone}
                        onChange={handleProfileChange}
                        disabled={!isEditing}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md ${
                          isEditing ? 'bg-white' : 'bg-gray-50 text-gray-500'
                        }`}
                        placeholder="请输入手机号"
                      />
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      个人简介
                    </label>
                    <textarea
                      name="bio"
                      rows={4}
                      value={profileData.bio}
                      onChange={handleProfileChange}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md ${
                        isEditing ? 'bg-white' : 'bg-gray-50 text-gray-500'
                      }`}
                      placeholder="请输入个人简介"
                    />
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex justify-end space-x-4">
                  {isEditing ? (
                    <>
                      <button
                        onClick={() => {
                          setIsEditing(false);
                          // 重置数据
                          if (user) {
                            setProfileData({
                              full_name: user.full_name || '',
                              phone: user.phone || '',
                              bio: user.bio || '',
                              avatar_url: user.avatar_url || '',
                            });
                          }
                        }}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                      >
                        取消
                      </button>
                      <button
                        onClick={handleSaveProfile}
                        disabled={isSaving}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                      >
                        {isSaving ? '保存中...' : '保存'}
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      编辑资料
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 安全设置标签页 */}
          {activeTab === 'security' && (
            <div className="p-6">
              <div className="space-y-6">
                {/* 修改密码 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">修改密码</h3>
                  
                  {errors.password_general && (
                    <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                      {errors.password_general}
                    </div>
                  )}
                  
                  <form onSubmit={handleChangePassword} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        原密码
                      </label>
                      <input
                        type="password"
                        name="old_password"
                        value={passwordData.old_password}
                        onChange={handlePasswordChange}
                        className={`w-full px-3 py-2 border rounded-md ${
                          errors.old_password ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="请输入原密码"
                      />
                      {errors.old_password && (
                        <p className="mt-1 text-sm text-red-600">{errors.old_password}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        新密码
                      </label>
                      <input
                        type="password"
                        name="new_password"
                        value={passwordData.new_password}
                        onChange={handlePasswordChange}
                        className={`w-full px-3 py-2 border rounded-md ${
                          errors.new_password ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="请输入新密码"
                      />
                      {errors.new_password && (
                        <p className="mt-1 text-sm text-red-600">{errors.new_password}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        密码至少8位，包含字母和数字
                      </p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        确认新密码
                      </label>
                      <input
                        type="password"
                        name="confirm_password"
                        value={passwordData.confirm_password}
                        onChange={handlePasswordChange}
                        className={`w-full px-3 py-2 border rounded-md ${
                          errors.confirm_password ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="请再次输入新密码"
                      />
                      {errors.confirm_password && (
                        <p className="mt-1 text-sm text-red-600">{errors.confirm_password}</p>
                      )}
                    </div>
                    
                    <div className="flex justify-end">
                      <button
                        type="submit"
                        disabled={isSaving}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                      >
                        {isSaving ? '修改中...' : '修改密码'}
                      </button>
                    </div>
                  </form>
                </div>

                {/* 账户信息 */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">账户信息</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">注册时间：</span>
                      <span className="text-gray-900">
                        {new Date(user.created_at).toLocaleDateString('zh-CN')}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">最后登录：</span>
                      <span className="text-gray-900">
                        {user.last_login_at 
                          ? new Date(user.last_login_at).toLocaleString('zh-CN')
                          : '从未登录'
                        }
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">账户状态：</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        user.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {user.is_active ? '正常' : '已禁用'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">邮箱验证：</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        user.is_verified 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {user.is_verified ? '已验证' : '未验证'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
