'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/service/auth-context';
import { apiClient } from '@/service/api';
import { App } from '@/types/user';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AppCenterPage() {
  const router = useRouter();
  const { user, authenticated, loading } = useAuth();
  
  const [apps, setApps] = useState<App[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // 检查登录状态
  useEffect(() => {
    if (!loading && !authenticated) {
      router.push('/auth/login');
    }
  }, [authenticated, loading, router]);

  // 获取用户应用
  useEffect(() => {
    if (authenticated) {
      fetchUserApps();
    }
  }, [authenticated]);

  const fetchUserApps = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.getUserApps();
      
      if (response.success) {
        setApps(response.apps);
      } else {
        setError('获取应用列表失败');
      }
    } catch (error) {
      console.error('Fetch apps error:', error);
      setError('获取应用列表失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };



  const getColorClass = (color?: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-100';
      case 'green':
        return 'bg-green-100';
      case 'purple':
        return 'bg-purple-100';
      case 'orange':
        return 'bg-orange-100';
      case 'red':
        return 'bg-red-100';
      case 'indigo':
        return 'bg-indigo-100';
      default:
        return 'bg-gray-100';
    }
  };

  const getTextColorClass = (color?: string) => {
    switch (color) {
      case 'blue':
        return 'text-blue-600';
      case 'green':
        return 'text-green-600';
      case 'purple':
        return 'text-purple-600';
      case 'orange':
        return 'text-orange-600';
      case 'red':
        return 'text-red-600';
      case 'indigo':
        return 'text-indigo-600';
      default:
        return 'text-gray-600';
    }
  };

  const getBgColorClass = (color?: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-50';
      case 'green':
        return 'bg-green-50';
      case 'purple':
        return 'bg-purple-50';
      case 'orange':
        return 'bg-orange-50';
      case 'red':
        return 'bg-red-50';
      case 'indigo':
        return 'bg-indigo-50';
      default:
        return 'bg-gray-50';
    }
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="应用中心"
      />

      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* 返回按钮 */}
          <div className="mb-8 text-left">
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
            >
              ← 返回首页
            </Link>
          </div>

          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            应用中心
          </h1>
          <p className="text-xl text-gray-600 mb-16 max-w-3xl mx-auto">
            欢迎，{user.full_name || user.username}！这里是您可以使用的所有应用
          </p>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

          {/* 应用网格 */}
          {apps.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {apps.map((app) => (
              <div
                key={app.id}
                className="group bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 cursor-pointer"
              >
                {/* 应用图标 */}
                <div className={`w-14 h-14 ${getColorClass(app.color)} rounded-lg flex items-center justify-center mb-6 group-hover:${getColorClass(app.color).replace('-100', '-200')} transition-colors`}>
                  <span className="text-2xl">{app.icon || '📱'}</span>
                </div>

                <h3 className={`text-xl font-semibold text-gray-900 mb-3 group-hover:${getTextColorClass(app.color)} transition-colors`}>
                  {app.name}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {app.description || '暂无描述'}
                </p>

                <div className="flex items-center justify-between">
                  <span className={`text-xs px-2 py-1 rounded-full ${getBgColorClass(app.color)} ${getTextColorClass(app.color)}`}>
                    已上线
                  </span>
                  {app.route_path ? (
                    <Link
                      href={app.route_path}
                      className={`flex items-center text-sm font-medium group-hover:translate-x-1 transition-transform cursor-pointer ${getTextColorClass(app.color)}`}
                    >
                      立即使用
                      <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </Link>
                  ) : (
                    <span className="text-gray-400 text-sm">暂不可用</span>
                  )}
                </div>
              </div>
            ))}
            </div>
          ) : (
            <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无可用应用</h3>
            <p className="text-gray-600 mb-6">
              您当前没有任何可用的应用权限，请联系管理员为您分配应用权限。
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              返回首页
            </Link>
            </div>
          )}

          {/* 底部信息 */}
          <div className="mt-12 text-center">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-medium text-gray-900 mb-2">需要更多应用权限？</h3>
            <p className="text-gray-600 mb-4">
              如果您需要访问其他应用，请联系系统管理员为您分配相应的权限。
            </p>
            <div className="flex justify-center">
              <Link
                href="/"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                返回首页
              </Link>
            </div>
          </div>
        </div>
      </div>
      </div>

      <Footer />
    </div>
  );
}
