'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/lib/auth-context';
import { apiClient } from '@/lib/api';
import { App } from '@/types/user';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AppCenterPage() {
  const router = useRouter();
  const { user, authenticated, loading } = useAuth();
  
  const [apps, setApps] = useState<App[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // 检查登录状态
  useEffect(() => {
    if (!loading && !authenticated) {
      router.push('/auth/login');
    }
  }, [authenticated, loading, router]);

  // 获取用户应用
  useEffect(() => {
    if (authenticated) {
      fetchUserApps();
    }
  }, [authenticated]);

  const fetchUserApps = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.getUserApps();
      
      if (response.success) {
        setApps(response.apps);
      } else {
        setError('获取应用列表失败');
      }
    } catch (error) {
      console.error('Fetch apps error:', error);
      setError('获取应用列表失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'production':
        return 'bg-green-100 text-green-800';
      case 'development':
        return 'bg-yellow-100 text-yellow-800';
      case 'maintenance':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'production':
        return '已上线';
      case 'development':
        return '开发中';
      case 'maintenance':
        return '维护中';
      default:
        return '未知';
    }
  };

  const getColorClass = (color?: string) => {
    switch (color) {
      case 'blue':
        return 'from-blue-500 to-blue-600';
      case 'green':
        return 'from-green-500 to-green-600';
      case 'purple':
        return 'from-purple-500 to-purple-600';
      case 'orange':
        return 'from-orange-500 to-orange-600';
      case 'red':
        return 'from-red-500 to-red-600';
      case 'indigo':
        return 'from-indigo-500 to-indigo-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="应用中心"
      />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">应用中心</h1>
          <p className="mt-2 text-gray-600">
            欢迎，{user.full_name || user.username}！这里是您可以使用的所有应用
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

        {/* 应用网格 */}
        {apps.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {apps.map((app) => (
              <div
                key={app.id}
                className="group bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100"
              >
                {/* 应用图标 */}
                <div className={`w-16 h-16 bg-gradient-to-br ${getColorClass(app.color)} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <span className="text-3xl">{app.icon || '📱'}</span>
                </div>

                {/* 应用信息 */}
                <div className="mb-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {app.name}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed mb-3 line-clamp-3">
                    {app.description || '暂无描述'}
                  </p>
                  
                  {/* 状态标签 */}
                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(app.status)}`}>
                      {getStatusText(app.status)}
                    </span>
                    {app.is_public && (
                      <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                        公开应用
                      </span>
                    )}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center justify-between">
                  {app.route_path && app.status === 'production' ? (
                    <Link
                      href={app.route_path}
                      className="flex items-center text-blue-600 text-sm font-medium group-hover:translate-x-1 transition-transform"
                    >
                      立即使用
                      <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </Link>
                  ) : app.status === 'development' ? (
                    <span className="text-gray-400 text-sm">开发中</span>
                  ) : app.status === 'maintenance' ? (
                    <span className="text-red-500 text-sm">维护中</span>
                  ) : (
                    <span className="text-gray-400 text-sm">暂不可用</span>
                  )}
                  
                  <div className="text-xs text-gray-400">
                    排序: {app.sort_order}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无可用应用</h3>
            <p className="text-gray-600 mb-6">
              您当前没有任何可用的应用权限，请联系管理员为您分配应用权限。
            </p>
            <button
              onClick={() => router.push('/')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              返回首页
            </button>
          </div>
        )}

        {/* 底部信息 */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-medium text-gray-900 mb-2">需要更多应用权限？</h3>
            <p className="text-gray-600 mb-4">
              如果您需要访问其他应用，请联系系统管理员为您分配相应的权限。
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => router.push('/account')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                账户设置
              </button>
              <button
                onClick={() => router.push('/')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                返回首页
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
