'use client';

import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function ReconciliationToolPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="智能对账工具"
      />

      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">

          <div className="text-6xl mb-8">📊</div>
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            智能对账工具
          </h1>
          <p className="text-xl text-gray-600 mb-16 max-w-3xl mx-auto">
            自动化财务对账与数据核验系统
          </p>
          
          {/* 功能介绍 */}
          <div className="bg-white/80 backdrop-blur-sm p-12 rounded-3xl shadow-lg max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              功能开发中
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              智能对账工具正在紧张开发中，敬请期待！
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-emerald-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-emerald-900 mb-2">自动对账</h3>
                <p className="text-emerald-700">智能匹配交易记录，自动识别差异和异常</p>
              </div>
              <div className="bg-cyan-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-cyan-900 mb-2">异常检测</h3>
                <p className="text-cyan-700">AI算法检测数据异常，提供详细分析报告</p>
              </div>
              <div className="bg-orange-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-orange-900 mb-2">报表生成</h3>
                <p className="text-orange-700">自动生成对账报表，支持多种格式导出</p>
              </div>
              <div className="bg-teal-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-teal-900 mb-2">风险预警</h3>
                <p className="text-teal-700">实时监控财务风险，及时预警潜在问题</p>
              </div>
            </div>
            
            <div className="text-sm text-gray-500">
              预计上线时间：2025年第四季度
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
