'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Policy, PolicyFormData } from '@/types/policy';
import { policyApi } from '@/lib/api/policies';
import PolicyTable from '@/components/policies/PolicyTable';
import PolicyModal from '@/components/policies/PolicyModal';
import PolicyDetailModal from '@/components/policies/PolicyDetailModal';
import ImportModal from '@/components/policies/ImportModal';
import SearchBar from '@/components/policies/SearchBar';
import DeleteConfirmModal from '@/components/policies/DeleteConfirmModal';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function PoliciesPage() {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    current_page: 1,
    per_page: 10
  });
  
  const [showModal, setShowModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [editingPolicy, setEditingPolicy] = useState<Policy | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPolicies, setSelectedPolicies] = useState<number[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingPolicyId, setDeletingPolicyId] = useState<number | null>(null);
  const [showBatchDeleteModal, setShowBatchDeleteModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [detailPolicy, setDetailPolicy] = useState<Policy | null>(null);

  const fetchPolicies = async (page = 1, search = '', perPage?: number) => {
    try {
      setLoading(true);
      const currentPerPage = perPage || pagination.per_page;
      const response = await policyApi.getAll(page, currentPerPage, search);
      setPolicies(response.policies);
      setPagination({
        total: response.total,
        pages: response.pages,
        current_page: response.current_page,
        per_page: response.per_page
      });
      // 清空选中项
      setSelectedPolicies([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取政策失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPolicies();
  }, []);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    fetchPolicies(1, term);
  };

  const handlePageChange = (page: number) => {
    fetchPolicies(page, searchTerm);
  };

  const handlePerPageChange = (perPage: number) => {
    fetchPolicies(1, searchTerm, perPage);
  };

  const handleCreate = () => {
    setEditingPolicy(null);
    setShowModal(true);
  };

  const handleEdit = (policy: Policy) => {
    setEditingPolicy(policy);
    setShowModal(true);
  };

  const handleDetail = (policy: Policy) => {
    setDetailPolicy(policy);
    setShowDetailModal(true);
  };

  const handleDelete = (id: number) => {
    setDeletingPolicyId(id);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!deletingPolicyId) return;

    try {
      await policyApi.delete(deletingPolicyId);
      fetchPolicies(pagination.current_page, searchTerm);
      setShowDeleteModal(false);
      setDeletingPolicyId(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除政策失败');
    }
  };

  const handleSave = async (data: PolicyFormData) => {
    try {
      if (editingPolicy) {
        await policyApi.update(editingPolicy.id, data);
      } else {
        await policyApi.create(data);
      }
      setShowModal(false);
      fetchPolicies(pagination.current_page, searchTerm);
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : '保存政策失败');
    }
  };

  const handleImportSuccess = () => {
    setShowImportModal(false);
    fetchPolicies(pagination.current_page, searchTerm);
  };

  // 处理复选框选择
  const handleSelectPolicy = (id: number) => {
    setSelectedPolicies(prev => {
      if (prev.includes(id)) {
        return prev.filter(policyId => policyId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectedPolicies.length === policies.length) {
      setSelectedPolicies([]);
    } else {
      setSelectedPolicies(policies.map(policy => policy.id));
    }
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedPolicies.length === 0) return;
    setShowBatchDeleteModal(true);
  };

  const confirmBatchDelete = async () => {
    try {
      // 并行删除所有选中的政策
      await Promise.all(selectedPolicies.map(id => policyApi.delete(id)));
      fetchPolicies(pagination.current_page, searchTerm);
      setShowBatchDeleteModal(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : '批量删除政策失败');
    }
  };

  if (loading && policies.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
        <Header
          showNavigation={false}
          breadcrumb="政策管理"
          showBackButton={true}
          backUrl="/agent-policy"
        />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-lg text-gray-700">正在加载政策...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="政策管理"
        showBackButton={true}
        backUrl="/agent-policy"
      />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">

        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">政策管理</h1>
          <p className="text-gray-600">管理您的旅行政策和佣金费率</p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50/80 backdrop-blur-sm border border-red-200 rounded-xl shadow-sm">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between">
          <SearchBar onSearch={handleSearch} />
          <div className="flex gap-3">
            {selectedPolicies.length > 0 && (
              <button
                onClick={handleBatchDelete}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all duration-200 cursor-pointer shadow-md hover:shadow-lg"
              >
                删除选中 ({selectedPolicies.length})
              </button>
            )}
            <button
              onClick={() => setShowImportModal(true)}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 cursor-pointer shadow-md hover:shadow-lg transform hover:scale-105"
            >
              导入Excel
            </button>
            <button
              onClick={handleCreate}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-200 cursor-pointer shadow-md hover:shadow-lg transform hover:scale-105"
            >
              添加政策
            </button>
          </div>
        </div>

      <PolicyTable
        policies={policies}
        pagination={pagination}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onDetail={handleDetail}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        onSelectPolicy={handleSelectPolicy}
        onSelectAll={handleSelectAll}
        selectedPolicies={selectedPolicies}
        loading={loading}
      />

      {showModal && (
        <PolicyModal
          policy={editingPolicy}
          onSave={handleSave}
          onClose={() => setShowModal(false)}
        />
      )}

      {showDetailModal && (
        <PolicyDetailModal
          policy={detailPolicy}
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setDetailPolicy(null);
          }}
        />
      )}

        {showImportModal && (
          <ImportModal
            onSuccess={handleImportSuccess}
            onClose={() => setShowImportModal(false)}
          />
        )}

        {/* 单个删除确认弹框 */}
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setDeletingPolicyId(null);
          }}
          onConfirm={confirmDelete}
          title="确认删除政策"
          message="您确定要删除这个政策吗？"
        />

        {/* 批量删除确认弹框 */}
        <DeleteConfirmModal
          isOpen={showBatchDeleteModal}
          onClose={() => setShowBatchDeleteModal(false)}
          onConfirm={confirmBatchDelete}
          title="确认批量删除"
          message={`您确定要删除选中的 ${selectedPolicies.length} 个政策吗？`}
          confirmText={`删除 ${selectedPolicies.length} 个政策`}
        />
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}