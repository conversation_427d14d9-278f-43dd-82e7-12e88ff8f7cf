'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import MultiModalImportModal from '@/components/policies/MultiModalImportModal';

export default function PolicyImportPage() {
  const [showImportModal, setShowImportModal] = useState(false);
  const router = useRouter();

  const handleImportSuccess = () => {
    setShowImportModal(false);
    // 可以选择跳转到政策列表页面查看导入结果
    router.push('/policies');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* 返回按钮 */}
          <div className="mb-8 text-left">
            <Link
              href="/agent-policy"
              className="inline-flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
            >
              ← 返回代理人政策问答
            </Link>
          </div>

          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            政策数据导入
          </h1>
          <p className="text-xl text-gray-600 mb-16 max-w-3xl mx-auto">
            使用AI技术从文档和图片文件中智能提取政策数据
          </p>
          
          {/* 功能介绍 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12">
            <div className="bg-white/70 backdrop-blur-sm p-8 rounded-xl shadow-sm">
              <div className="text-4xl mb-6">📄</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">文档支持</h3>
              <p className="text-gray-600">PDF, Word, Excel, PowerPoint, TXT, CSV, HTML, XML, EPUB等</p>
            </div>

            <div className="bg-white/70 backdrop-blur-sm p-8 rounded-xl shadow-sm">
              <div className="text-4xl mb-6">🖼️</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">图片识别</h3>
              <p className="text-gray-600">JPG, PNG, GIF, WebP, SVG等图片格式</p>
            </div>
          </div>

          {/* 主要操作区域 */}
          <div className="bg-white/80 backdrop-blur-sm p-12 rounded-3xl shadow-lg max-w-4xl mx-auto">
            <div className="text-6xl mb-8">🤖</div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              AI智能数据提取
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              上传您的文件，我们的AI将自动识别并提取其中的政策相关信息，
              包括政策名称、佣金费率、日期范围、航线信息等。
            </p>
            
            <div className="space-y-4 mb-8">
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                <span className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  智能识别
                </span>
                <span className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  数据验证
                </span>
                <span className="flex items-center">
                  <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                  批量导入
                </span>
              </div>
            </div>

            <button
              onClick={() => setShowImportModal(true)}
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg cursor-pointer"
            >
              开始导入数据
            </button>
          </div>

          {/* 使用步骤 */}
          <div className="mt-16 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">使用步骤</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">1</span>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">上传文件</h4>
                <p className="text-gray-600">选择包含政策信息的文件</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-purple-600">2</span>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">AI分析</h4>
                <p className="text-gray-600">AI自动提取政策数据</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-green-600">3</span>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">确认导入</h4>
                <p className="text-gray-600">预览数据并完成导入</p>
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* 导入模态框 */}
      {showImportModal && (
        <MultiModalImportModal
          onSuccess={handleImportSuccess}
          onClose={() => setShowImportModal(false)}
        />
      )}
    </div>
  );
}
