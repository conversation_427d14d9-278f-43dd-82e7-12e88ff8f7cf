'use client';

import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function IntelligentQAPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="智能问数"
      />

      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">

          <div className="text-6xl mb-8">🧠</div>
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            智能问数
          </h1>
          <p className="text-xl text-gray-600 mb-16 max-w-3xl mx-auto">
            基于AI的智能数据查询与分析系统
          </p>
          
          {/* 功能介绍 */}
          <div className="bg-white/80 backdrop-blur-sm p-12 rounded-3xl shadow-lg max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              功能开发中
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              智能问数系统正在紧张开发中，敬请期待！
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">自然语言查询</h3>
                <p className="text-blue-700">使用自然语言描述查询需求，AI自动转换为数据查询</p>
              </div>
              <div className="bg-green-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-green-900 mb-2">智能分析</h3>
                <p className="text-green-700">自动分析数据趋势，生成可视化图表和洞察报告</p>
              </div>
            </div>
            
            <div className="text-sm text-gray-500">
              预计上线时间：2025年第二季度
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
