'use client';

import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AgentPolicyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="代理人政策问答"
      />

      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">

          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            代理人政策问答
          </h1>
          <p className="text-xl text-gray-600 mb-16 max-w-3xl mx-auto">
            代理人政策管理与智能问答系统
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Link
              href="/policies"
              className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <div className="text-4xl mb-6">📋</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                政策管理
              </h3>
              <p className="text-gray-600">
                创建、编辑和维护各种政策，支持批量导入和数据分析
              </p>
            </Link>

            <Link
              href="/policy-import"
              className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <div className="text-4xl mb-6">📊</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                政策数据导入
              </h3>
              <p className="text-gray-600">
                从多种文件中提取政策数据，支持文档、图片等格式
              </p>
            </Link>

            <div className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
              <div className="text-4xl mb-6">📈</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                数据分析
              </h3>
              <p className="text-gray-600">
                按客户、航空公司、航线或其他维度进行数据分析和统计
              </p>
            </div>
          </div>

          {/* 功能特色 */}
          <div className="mt-20 max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-12">功能特色</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-sm">
                <div className="text-3xl mb-4">🤖</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">AI智能提取</h3>
                <p className="text-sm text-gray-600">使用APS平台的大模型API自动提取政策数据</p>
              </div>
              
              <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-sm">
                <div className="text-3xl mb-4">📄</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">多格式支持</h3>
                <p className="text-sm text-gray-600">支持PDF、Word、Excel、图片等多种文件格式</p>
              </div>
              
              <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-sm">
                <div className="text-3xl mb-4">⚡</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">批量处理</h3>
                <p className="text-sm text-gray-600">支持批量导入和处理，提高工作效率</p>
              </div>
              
              <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-sm">
                <div className="text-3xl mb-4">🔍</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">智能搜索</h3>
                <p className="text-sm text-gray-600">强大的搜索和筛选功能，快速找到所需政策</p>
              </div>
            </div>
          </div>

          {/* 使用统计 */}
          <div className="mt-16 bg-white/50 backdrop-blur-sm p-8 rounded-xl shadow-sm max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">使用统计</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">1,234</div>
                <div className="text-gray-600">活跃政策</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">5,678</div>
                <div className="text-gray-600">文件处理</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">98.5%</div>
                <div className="text-gray-600">准确率</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
