'use client';

import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function PolicyChatPage() {
  return (
    <div className="h-screen flex flex-col">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="政策问答"
      />

      {/* 返回按钮 - 固定在顶部 */}
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <Link
          href="/agent-policy"
          className="inline-flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
        >
          ← 返回代理人政策问答
        </Link>
      </div>

      {/* 全屏 iframe */}
      <div className="flex-1 overflow-hidden">
        <iframe
          src="http://192.168.1.104/chatbot/qSpmsZVIznTRVl14"
          style={{
            width: '100%',
            height: '100%',
            border: 'none'
          }}
          allow="microphone"
          title="政策问答系统"
        />
      </div>
    </div>
  );
}
