'use client';

import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function PolicyChatPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      {/* 导航栏 */}
      <Header
        showNavigation={false}
        breadcrumb="政策问答"
      />

      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* 返回按钮 */}
          <div className="mb-8 text-left">
            <Link
              href="/agent-policy"
              className="inline-flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
            >
              ← 返回代理人政策问答
            </Link>
          </div>

          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            政策问答
          </h1>
          <p className="text-xl text-gray-600 mb-16 max-w-3xl mx-auto">
            智能政策问答系统，快速获取政策相关信息和解答
          </p>

          {/* 问答界面容器 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg overflow-hidden">
            <iframe
              src="http://192.168.1.104/chatbot/qSpmsZVIznTRVl14"
              style={{
                width: '100%',
                height: '100%',
                minHeight: '700px',
                border: 'none'
              }}
              allow="microphone"
              title="政策问答系统"
            />
          </div>

          {/* 使用提示 */}
          <div className="mt-12 max-w-4xl mx-auto">
            <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">使用提示</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-600">
                <div className="text-center">
                  <div className="text-2xl mb-2">💬</div>
                  <p>直接输入您的政策相关问题</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-2">🎤</div>
                  <p>支持语音输入，更便捷的交互方式</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-2">⚡</div>
                  <p>AI智能解答，快速获取准确信息</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
