import { Policy } from '@/types/policy';

interface PolicyDetailModalProps {
  policy: Policy | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function PolicyDetailModal({ policy, isOpen, onClose }: PolicyDetailModalProps) {
  if (!isOpen || !policy) return null;

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const formatPassengerType = (passengerType?: string) => {
    if (!passengerType) return '-';
    const typeMap: { [key: string]: string } = {
      'ADT': '成人',
      'CHD': '儿童',
      'INF': '婴儿'
    };
    return passengerType.split(',').map(type => typeMap[type.trim()] || type.trim()).join(', ');
  };

  const formatCommissionRate = (rate?: number) => {
    if (!rate) return '-';
    return `${(rate * 100).toFixed(2)}%`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">政策详情</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors cursor-pointer"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 基本信息 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                旅客类型
              </label>
              <p className="text-sm text-gray-900">{formatPassengerType(policy.passenger_type)}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                佣金费率
              </label>
              <p className="text-sm text-gray-900">{formatCommissionRate(policy.commission_rate)}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                航空公司
              </label>
              <p className="text-sm text-gray-900">{policy.airline || '-'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                舱位等级
              </label>
              <p className="text-sm text-gray-900">{policy.cabin_classes || '-'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                出发地
              </label>
              <p className="text-sm text-gray-900">{policy.origin || '-'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                目的地
              </label>
              <p className="text-sm text-gray-900">{policy.destination || '-'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                旅行日期
              </label>
              <p className="text-sm text-gray-900">
                {formatDate(policy.travel_start_date)} 至 {formatDate(policy.travel_end_date)}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                销售日期
              </label>
              <p className="text-sm text-gray-900">
                {formatDate(policy.sale_start_date)} 至 {formatDate(policy.sale_end_date)}
              </p>
            </div>
          </div>

          {/* 备注信息 */}
          {policy.remark && (
            <div className="mt-6">
              <h4 className="text-md font-medium text-gray-900 border-b pb-2 mb-4">备注</h4>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{policy.remark}</p>
            </div>
          )}

          {/* 创建和更新时间 */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500">
              <div>
                <span className="font-medium">创建时间：</span>
                {policy.created_at ? formatDate(policy.created_at) : '-'}
              </div>
              <div>
                <span className="font-medium">更新时间：</span>
                {policy.updated_at ? formatDate(policy.updated_at) : '-'}
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors cursor-pointer"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}