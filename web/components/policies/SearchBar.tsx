import { useState } from 'react';

interface SearchBarProps {
  onSearch: (term: string) => void;
}

export default function SearchBar({ onSearch }: SearchBarProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchTerm);
  };

  const handleClear = () => {
    setSearchTerm('');
    onSearch('');
  };

  return (
    <form onSubmit={handleSubmit} className="flex-1 max-w-md">
      <div className="relative">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="按政策名称、航空公司或航线搜索..."
          className="w-full px-4 py-2 pr-20 bg-white/80 backdrop-blur-sm border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
        />
        <div className="absolute inset-y-0 right-0 flex items-center">
          {searchTerm && (
            <button
              type="button"
              onClick={handleClear}
              className="px-2 py-1 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors duration-200"
            >
              ✕
            </button>
          )}
          <button
            type="submit"
            className="px-3 py-1 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors duration-200"
          >
            🔍
          </button>
        </div>
      </div>
    </form>
  );
}
