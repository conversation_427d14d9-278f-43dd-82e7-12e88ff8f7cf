import { useState, useRef } from 'react';
import { policyApi } from '@/service/api/policies';
import { ImportResult } from '@/types/policy';

interface ImportModalProps {
  onSuccess: () => void;
  onClose: () => void;
}

export default function ImportModal({ onSuccess, onClose }: ImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [downloadingTemplate, setDownloadingTemplate] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
      if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        setFile(selectedFile);
        setError(null);
      } else {
        setError('请选择有效的Excel文件（.xlsx 或 .xls）');
        setFile(null);
      }
    }
  };

  const handleImport = async () => {
    if (!file) return;

    setLoading(true);
    setError(null);

    try {
      const importResult = await policyApi.importFromExcel(file);
      setResult(importResult);
      
      if (importResult.errors.length === 0) {
        setTimeout(() => {
          onSuccess();
        }, 2000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '导入文件失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      setDownloadingTemplate(true);
      setError(null);
      await policyApi.downloadTemplate();
    } catch (err) {
      setError(err instanceof Error ? err.message : '下载模板失败');
    } finally {
      setDownloadingTemplate(false);
    }
  };

  const handleReset = () => {
    setFile(null);
    setResult(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            从Excel导入政策
          </h3>

          {!result && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择Excel文件
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                />
                <p className="text-xs text-gray-500 mt-1">
                  支持格式：.xlsx, .xls
                </p>
              </div>

              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-sm font-medium text-blue-900">预期列名：</h4>
                  <button
                    onClick={handleDownloadTemplate}
                    disabled={downloadingTemplate}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                  >
                    {downloadingTemplate ? '下载中...' : '下载模板'}
                  </button>
                </div>
                <ul className="text-xs text-blue-800 space-y-1">
                  <li>• 政策名称（必填）</li>
                  <li>• 佣金费率</li>
                  <li>• 承运开始/结束日期</li>
                  <li>• 销售开始/结束日期</li>
                  <li>• 出发地/目的地</li>
                  <li>• 航空公司、舱位等级</li>
                  <li>• 排除规则</li>
                </ul>
                <p className="text-xs text-blue-600 mt-2">
                  💡 建议先下载模板，按照格式填写数据后再导入
                </p>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}
            </>
          )}

          {result && (
            <div className="mb-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-md mb-4">
                <h4 className="text-sm font-medium text-green-900 mb-2">导入结果：</h4>
                <p className="text-sm text-green-800">
                  成功导入 {result.imported_count} 条政策
                </p>
              </div>

              {result.errors.length > 0 && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-md mb-4">
                  <h4 className="text-sm font-medium text-red-900 mb-2">错误信息：</h4>
                  <ul className="text-sm text-red-800 space-y-1">
                    {result.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {result.policies.length > 0 && (
                <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                  <table className="min-w-full text-xs">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-2 py-1 text-left">旅客类型</th>
                        <th className="px-2 py-1 text-left">费率</th>
                        <th className="px-2 py-1 text-left">航线</th>
                      </tr>
                    </thead>
                    <tbody>
                      {result.policies.slice(0, 5).map((policy, index) => (
                        <tr key={index} className="border-t">
                          <td className="px-2 py-1">{policy.passenger_type || '-'}</td>
                          <td className="px-2 py-1">
                            {policy.commission_rate ? `${(policy.commission_rate * 100).toFixed(2)}%` : '-'}
                          </td>
                          <td className="px-2 py-1">
                            {policy.origin && policy.destination 
                              ? `${policy.origin}-${policy.destination}` 
                              : '-'
                            }
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {result.policies.length > 5 && (
                    <div className="px-2 py-1 text-xs text-gray-500 bg-gray-50">
                      ... 还有 {result.policies.length - 5} 条记录
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 cursor-pointer"
            >
              关闭
            </button>
            {!result && (
              <button
                onClick={handleImport}
                disabled={!file || loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
              >
                {loading ? '导入中...' : '导入'}
              </button>
            )}
            {result && result.errors.length > 0 && (
              <button
                onClick={handleReset}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 cursor-pointer"
              >
                重新尝试
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
