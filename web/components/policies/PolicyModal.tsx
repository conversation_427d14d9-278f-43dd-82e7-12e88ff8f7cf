import { useState, useEffect } from 'react';
import { Policy, PolicyFormData } from '@/types/policy';

interface PolicyModalProps {
  policy?: Policy | null;
  onSave: (data: PolicyFormData) => Promise<void>;
  onClose: () => void;
}

export default function PolicyModal({ policy, onSave, onClose }: PolicyModalProps) {
  const [formData, setFormData] = useState<PolicyFormData>({
    passenger_type: '',
    commission_rate: undefined,
    travel_start_date: '',
    travel_end_date: '',
    sale_start_date: '',
    sale_end_date: '',
    origin: '',
    destination: '',
    cabin_classes: '',
    airline: '',
    remark: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (policy) {
      setFormData({
        passenger_type: policy.passenger_type || '',
        commission_rate: policy.commission_rate,
        travel_start_date: policy.travel_start_date || '',
        travel_end_date: policy.travel_end_date || '',
        sale_start_date: policy.sale_start_date || '',
        sale_end_date: policy.sale_end_date || '',
        origin: policy.origin || '',
        destination: policy.destination || '',
        cabin_classes: policy.cabin_classes || '',
        airline: policy.airline || '',
        remark: policy.remark || ''
      });
    }
  }, [policy]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const submitData: PolicyFormData = { ...formData };
      
      // 将空字符串转换为 undefined
      Object.keys(submitData).forEach(key => {
        if (submitData[key as keyof PolicyFormData] === '') {
          (submitData as any)[key] = undefined;
        }
      });

      await onSave(submitData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存政策失败');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'commission_rate' ? (value ? parseFloat(value) : undefined) : value
    }));
  };

  // 修复日期字段的处理
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {policy ? '编辑政策' : '添加新政策'}
          </h3>

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  承运开始日期
                </label>
                <input
                  type="date"
                  name="travel_start_date"
                  value={formData.travel_start_date}
                  onChange={handleDateChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  承运结束日期
                </label>
                <input
                  type="date"
                  name="travel_end_date"
                  value={formData.travel_end_date}
                  onChange={handleDateChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  销售开始日期
                </label>
                <input
                  type="date"
                  name="sale_start_date"
                  value={formData.sale_start_date}
                  onChange={handleDateChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  销售结束日期
                </label>
                <input
                  type="date"
                  name="sale_end_date"
                  value={formData.sale_end_date}
                  onChange={handleDateChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  出发地
                </label>
                <input
                  type="text"
                  name="origin"
                  value={formData.origin}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  目的地
                </label>
                <input
                  type="text"
                  name="destination"
                  value={formData.destination}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  航空公司
                </label>
                <input
                  type="text"
                  name="airline"
                  value={formData.airline}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  舱位等级
                </label>
                <input
                  type="text"
                  name="cabin_classes"
                  value={formData.cabin_classes}
                  onChange={handleChange}
                  placeholder="例如：Y, C, F"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  佣金费率 (%)
                </label>
                <input
                  type="number"
                  name="commission_rate"
                  value={formData.commission_rate || ''}
                  onChange={handleChange}
                  step="0.0001"
                  min="0"
                  max="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  旅客类型
                </label>
                <div className="space-y-2">
                  <div className="flex flex-wrap gap-2">
                    {['ADT', 'CHD', 'INF'].map((type) => {
                      const isSelected = formData.passenger_type?.split(',').map(t => t.trim()).includes(type);
                      return (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={(e) => {
                              const currentTypes = formData.passenger_type?.split(',').map(t => t.trim()).filter(t => t) || [];
                              let newTypes;
                              if (e.target.checked) {
                                newTypes = [...currentTypes, type];
                              } else {
                                newTypes = currentTypes.filter(t => t !== type);
                              }
                              setFormData(prev => ({
                                ...prev,
                                passenger_type: newTypes.join(',')
                              }));
                            }}
                            className="mr-1"
                          />
                          <span className="text-xs">
                            {type}
                          </span>
                        </label>
                      );
                    })}
                  </div>
                  <p className="text-xs text-gray-500">可选择多个</p>
                </div>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  备注
                </label>
                <textarea
                  name="remark"
                  value={formData.remark}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 cursor-pointer"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
              >
                {loading ? '保存中...' : '保存'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
