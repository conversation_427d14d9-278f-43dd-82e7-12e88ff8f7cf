import { useState, useRef } from 'react';
import { policyApi } from '@/lib/api/policies';
import { ImportResult } from '@/types/policy';

interface MultiModalImportModalProps {
  onSuccess: () => void;
  onClose: () => void;
}

export default function MultiModalImportModal({ onSuccess, onClose }: MultiModalImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [extracting, setExtracting] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 支持的文件类型
  const supportedTypes = {
    document: ['txt', 'md', 'markdown', 'pdf', 'html', 'xlsx', 'xls', 'docx', 'csv', 'pptx', 'ppt', 'xml', 'epub'],
    image: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  };

  const getAllSupportedExtensions = () => {
    return Object.values(supportedTypes).flat();
  };

  const getFileType = (filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase() || '';
    
    for (const [type, extensions] of Object.entries(supportedTypes)) {
      if (extensions.includes(extension)) {
        return type;
      }
    }
    return 'custom';
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
      const supportedExtensions = getAllSupportedExtensions();
      
      if (fileExtension && supportedExtensions.includes(fileExtension)) {
        setFile(selectedFile);
        setError(null);
        setExtractedData(null);
        setResult(null);
      } else {
        setError(`不支持的文件类型。支持的格式：${supportedExtensions.join(', ')}`);
        setFile(null);
      }
    }
  };

  const handleExtractData = async () => {
    if (!file) return;

    setExtracting(true);
    setError(null);

    try {
      const extractedResult = await policyApi.extractFromMultiModal(file);
      setExtractedData(extractedResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : '数据提取失败');
    } finally {
      setExtracting(false);
    }
  };

  const handleImport = async () => {
    if (!extractedData) return;

    setLoading(true);
    setError(null);

    try {
      const importResult = await policyApi.importFromExtractedData(extractedData);
      setResult(importResult);
      
      if (importResult.errors.length === 0) {
        setTimeout(() => {
          onSuccess();
        }, 2000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '导入数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFile(null);
    setResult(null);
    setError(null);
    setExtractedData(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            政策数据导入 - 多模态文件支持
          </h3>

          {!result && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择文件
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleFileChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                />
                <p className="text-xs text-gray-500 mt-1">
                  支持文档、图片等多种格式
                </p>
              </div>

              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <h4 className="text-sm font-medium text-blue-900 mb-2">支持的文件类型：</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-blue-800">
                  <div>
                    <strong>文档：</strong> PDF, Word, Excel, PowerPoint, TXT, CSV 等
                  </div>
                  <div>
                    <strong>图片：</strong> JPG, PNG, GIF, WebP, SVG 等
                  </div>
                </div>
                <p className="text-xs text-blue-600 mt-2">
                  💡 AI将自动识别文件内容并提取政策相关数据
                </p>
              </div>

              {file && !extractedData && (
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-900">已选择文件：</p>
                      <p className="text-sm text-green-800">{file.name}</p>
                      <p className="text-xs text-green-600">
                        类型：{getFileType(file.name)} | 大小：{(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <button
                      onClick={handleExtractData}
                      disabled={extracting}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                    >
                      {extracting ? '提取中...' : '提取数据'}
                    </button>
                  </div>
                </div>
              )}

              {extractedData && (
                <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <h4 className="text-sm font-medium text-yellow-900 mb-2">提取的数据预览：</h4>
                  <div className="max-h-40 overflow-y-auto bg-white p-2 rounded border text-xs">
                    <pre className="whitespace-pre-wrap">{JSON.stringify(extractedData, null, 2)}</pre>
                  </div>
                  <p className="text-xs text-yellow-600 mt-2">
                    请确认数据正确后点击导入
                  </p>
                </div>
              )}
            </>
          )}

          {result && (
            <div className="mb-4">
              <div className={`p-4 rounded-md ${result.errors.length === 0 ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
                <h4 className={`text-sm font-medium mb-2 ${result.errors.length === 0 ? 'text-green-900' : 'text-yellow-900'}`}>
                  导入结果
                </h4>
                <p className={`text-sm ${result.errors.length === 0 ? 'text-green-800' : 'text-yellow-800'}`}>
                  成功导入 {result.imported_count} 条政策记录
                </p>
                
                {result.errors.length > 0 && (
                  <div className="mt-3">
                    <h5 className="text-sm font-medium text-red-900 mb-1">错误信息：</h5>
                    <ul className="text-xs text-red-800 space-y-1 max-h-32 overflow-y-auto">
                      {result.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 cursor-pointer"
            >
              关闭
            </button>
            {extractedData && !result && (
              <button
                onClick={handleImport}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
              >
                {loading ? '导入中...' : '导入数据'}
              </button>
            )}
            {result && result.errors.length > 0 && (
              <button
                onClick={handleReset}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 cursor-pointer"
              >
                重新尝试
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
