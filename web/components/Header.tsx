'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import UserDropdown from '@/components/UserDropdown';

interface HeaderProps {
  title?: string;
  showNavigation?: boolean;
  className?: string;
  breadcrumb?: string;
  showBackButton?: boolean;
  backUrl?: string;
}

export default function Header({
  title = "XXX管理平台",
  showNavigation = true,
  className = "",
  breadcrumb,
  showBackButton = false,
  backUrl = "/"
}: HeaderProps) {
  const router = useRouter();

  const scrollToFeatures = () => {
    const featuresSection = document.getElementById('features-section');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // 如果不在首页，跳转到首页的功能区域
      router.push('/#features-section');
    }
  };

  return (
    <nav className={`bg-white/80 backdrop-blur-sm border-b border-gray-100 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo和标题 */}
          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
            >
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-bold">X</span>
              </div>
              <span className="text-xl font-bold text-gray-900">{title}</span>
            </Link>

            {/* 面包屑导航 */}
            {breadcrumb && (
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span>/</span>
                <span>{breadcrumb}</span>
              </div>
            )}

            {/* 返回按钮 */}
            {showBackButton && (
              <Link
                href={backUrl}
                className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>返回</span>
              </Link>
            )}
          </div>

          {/* 导航菜单 */}
          {showNavigation && (
            <div className="hidden md:flex items-center space-x-8">
              <button
                onClick={scrollToFeatures}
                className="text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
              >
                产品功能
              </button>
              <Link 
                href="#" 
                className="text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
              >
                解决方案
              </Link>
              <Link 
                href="#" 
                className="text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
              >
                关于我们
              </Link>
              <UserDropdown />
            </div>
          )}

          {/* 如果不显示导航，只显示用户下拉菜单 */}
          {!showNavigation && (
            <div className="flex items-center">
              <UserDropdown />
            </div>
          )}

          {/* 移动端菜单按钮 */}
          <div className="md:hidden">
            <UserDropdown />
          </div>
        </div>
      </div>
    </nav>
  );
}
