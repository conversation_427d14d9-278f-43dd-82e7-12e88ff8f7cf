import { Policy, PolicyFormData, PolicyListResponse, ImportResult } from '@/types/policy';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export const policyApi = {
  async downloadTemplate(): Promise<void> {
    const response = await fetch(`${API_BASE}/api/policies/template`);
    if (!response.ok) {
      throw new Error('下载模板失败');
    }
    
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '政策导入模板.xlsx';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  },

  async importFromExcel(file: File): Promise<ImportResult> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE}/api/policies/import`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('导入失败');
    }

    return response.json();
  },

  async extractFromMultiModal(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE}/api/policies/extract-multimodal`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('多模态数据提取失败');
    }

    return response.json();
  },

  async importFromExtractedData(extractedData: any): Promise<ImportResult> {
    const response = await fetch(`${API_BASE}/api/policies/import-extracted`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(extractedData),
    });

    if (!response.ok) {
      throw new Error('导入提取的数据失败');
    }

    return response.json();
  },

  async getAll(page: number = 1, perPage: number = 20, search?: string): Promise<PolicyListResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString(),
    });

    if (search) {
      params.append('search', search);
    }

    const response = await fetch(`${API_BASE}/api/policies?${params}`);
    if (!response.ok) {
      throw new Error('获取政策列表失败');
    }

    return response.json();
  },

  async create(data: PolicyFormData): Promise<Policy> {
    const response = await fetch(`${API_BASE}/api/policies`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('创建政策失败');
    }

    return response.json();
  },

  async update(id: number, data: PolicyFormData): Promise<Policy> {
    const response = await fetch(`${API_BASE}/api/policies/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('更新政策失败');
    }

    return response.json();
  },

  async delete(id: number): Promise<void> {
    const response = await fetch(`${API_BASE}/api/policies/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('删除政策失败');
    }
  },

  async getById(id: number): Promise<Policy> {
    const response = await fetch(`${API_BASE}/api/policies/${id}`);
    if (!response.ok) {
      throw new Error('获取政策详情失败');
    }

    return response.json();
  },
};
