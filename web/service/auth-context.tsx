'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, AuthState } from '@/types/user';
import { apiClient } from '@/service/api';

interface AuthContextType extends AuthState {
  login: (usernameOrEmail: string, password: string, remember?: boolean) => Promise<{ success: boolean; message: string }>;
  logout: () => Promise<void>;
  updateUser: (user: User) => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    authenticated: false,
    loading: true,
  });

  // 检查认证状态
  const checkAuth = async () => {
    try {
      const response = await apiClient.checkAuth();
      setAuthState({
        user: response.user,
        authenticated: response.authenticated,
        loading: false,
      });
    } catch (error) {
      console.error('Check auth failed:', error);
      setAuthState({
        user: null,
        authenticated: false,
        loading: false,
      });
    }
  };

  // 登录
  const login = async (usernameOrEmail: string, password: string, remember = false) => {
    try {
      const response = await apiClient.login({
        username_or_email: usernameOrEmail,
        password,
        remember,
      });

      if (response.success && response.user) {
        setAuthState({
          user: response.user,
          authenticated: true,
          loading: false,
        });
      }

      return { success: response.success, message: response.message };
    } catch (error) {
      console.error('Login failed:', error);
      return { success: false, message: '登录失败，请稍后重试' };
    }
  };

  // 登出
  const logout = async () => {
    try {
      await apiClient.logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setAuthState({
        user: null,
        authenticated: false,
        loading: false,
      });
    }
  };

  // 更新用户信息
  const updateUser = (user: User) => {
    setAuthState(prev => ({
      ...prev,
      user,
    }));
  };

  // 刷新用户信息
  const refreshUser = async () => {
    if (!authState.authenticated) return;

    try {
      const response = await apiClient.getCurrentUser();
      if (response.success && response.user) {
        setAuthState(prev => ({
          ...prev,
          user: response.user,
        }));
      }
    } catch (error) {
      console.error('Refresh user failed:', error);
    }
  };

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuth();
  }, []);

  const value: AuthContextType = {
    ...authState,
    login,
    logout,
    updateUser,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
