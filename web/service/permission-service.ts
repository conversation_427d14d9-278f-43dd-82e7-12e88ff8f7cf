import { apiClient } from '@/service/api';
import { App } from '@/types/user';

export interface TrialApplicationData {
  appSlug: string;
  appName: string;
  userEmail: string;
  userName: string;
  reason?: string;
  company?: string;
  phone?: string;
}

export class PermissionService {
  /**
   * 检查用户是否有应用权限
   */
  static async checkAppPermission(appSlug: string): Promise<{
    hasPermission: boolean;
    app?: App;
    message?: string;
  }> {
    try {
      const response = await apiClient.checkAppPermission(appSlug);
      
      if (response.success) {
        return {
          hasPermission: response.has_permission,
        };
      } else {
        return {
          hasPermission: false,
          message: response.message || '权限检查失败',
        };
      }
    } catch (error) {
      console.error('Permission check failed:', error);
      return {
        hasPermission: false,
        message: '权限检查失败，请稍后重试',
      };
    }
  }

  /**
   * 获取应用详情
   */
  static async getAppDetails(appSlug: string): Promise<{
    success: boolean;
    app?: App;
    hasPermission?: boolean;
    message?: string;
  }> {
    try {
      const response = await apiClient.getAppBySlug(appSlug);
      
      if (response.success) {
        return {
          success: true,
          app: response.app,
          hasPermission: response.has_permission,
        };
      } else {
        return {
          success: false,
          message: response.message || '获取应用信息失败',
        };
      }
    } catch (error) {
      console.error('Get app details failed:', error);
      return {
        success: false,
        message: '获取应用信息失败，请稍后重试',
      };
    }
  }

  /**
   * 发送试用申请邮件
   */
  static async sendTrialApplication(data: TrialApplicationData): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      const response = await apiClient.sendTrialApplication(data);
      
      if (response.success) {
        return {
          success: true,
          message: '试用申请已提交，我们会尽快处理您的申请',
        };
      } else {
        return {
          success: false,
          message: response.message || '试用申请提交失败',
        };
      }
    } catch (error) {
      console.error('Send trial application failed:', error);
      return {
        success: false,
        message: '试用申请提交失败，请稍后重试',
      };
    }
  }

  /**
   * 检查用户登录状态并验证应用权限
   */
  static async checkAccessPermission(appSlug: string, isAuthenticated: boolean): Promise<{
    canAccess: boolean;
    needLogin: boolean;
    needTrial: boolean;
    app?: App;
    message?: string;
  }> {
    // 如果用户未登录
    if (!isAuthenticated) {
      return {
        canAccess: false,
        needLogin: true,
        needTrial: false,
        message: '请先登录以访问此应用',
      };
    }

    // 检查应用权限
    const appResult = await this.getAppDetails(appSlug);
    
    if (!appResult.success) {
      return {
        canAccess: false,
        needLogin: false,
        needTrial: false,
        message: appResult.message,
      };
    }

    const app = appResult.app!;
    const hasPermission = appResult.hasPermission;

    // 如果是公开应用或用户有权限
    if (app.is_public || hasPermission) {
      return {
        canAccess: true,
        needLogin: false,
        needTrial: false,
        app,
      };
    }

    // 用户没有权限，需要申请试用
    return {
      canAccess: false,
      needLogin: false,
      needTrial: true,
      app,
      message: '您暂无此应用的使用权限，可以申请试用',
    };
  }
}

export default PermissionService;
