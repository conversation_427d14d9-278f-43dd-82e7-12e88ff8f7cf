import { 
  LoginRequest, 
  RegisterRequest, 
  UpdateProfileRequest, 
  ChangePasswordRequest,
  AuthResponse,
  AppsResponse,
  AppPermissionResponse,
  User,
  App
} from '@/types/user';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6000';

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // 包含cookies用于session管理
      ...options,
    };

    try {
      const response = await fetch(url, config);

      // 对于认证相关的错误，直接返回响应内容而不抛出错误
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 401 || response.status === 403) {
          return errorData as T;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // 认证相关API
  async login(data: LoginRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async logout(): Promise<AuthResponse> {
    return this.request<AuthResponse>('/api/auth/logout', {
      method: 'POST',
    });
  }

  async getCurrentUser(): Promise<{ success: boolean; user: User }> {
    return this.request<{ success: boolean; user: User }>('/api/auth/me');
  }

  async checkAuth(): Promise<{ success: boolean; authenticated: boolean; user: User | null }> {
    return this.request<{ success: boolean; authenticated: boolean; user: User | null }>('/api/auth/check');
  }

  async updateProfile(data: UpdateProfileRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/api/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async changePassword(data: ChangePasswordRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/api/auth/change-password', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // 应用相关API
  async getUserApps(): Promise<AppsResponse> {
    return this.request<AppsResponse>('/api/apps/');
  }

  async getAllApps(): Promise<AppsResponse> {
    return this.request<AppsResponse>('/api/apps/all');
  }

  async getAppBySlug(slug: string): Promise<{ success: boolean; app: App; has_permission: boolean }> {
    return this.request<{ success: boolean; app: App; has_permission: boolean }>(`/api/apps/${slug}`);
  }

  async checkAppPermission(slug: string): Promise<AppPermissionResponse> {
    return this.request<AppPermissionResponse>(`/api/apps/check-permission/${slug}`);
  }

  async grantAppPermission(appId: number, userId: number): Promise<AuthResponse> {
    return this.request<AuthResponse>(`/api/apps/${appId}/grant-permission`, {
      method: 'POST',
      body: JSON.stringify({ user_id: userId }),
    });
  }

  async revokeAppPermission(appId: number, userId: number): Promise<AuthResponse> {
    return this.request<AuthResponse>(`/api/apps/${appId}/revoke-permission`, {
      method: 'POST',
      body: JSON.stringify({ user_id: userId }),
    });
  }

  async createApp(data: Partial<App>): Promise<AuthResponse> {
    return this.request<AuthResponse>('/api/apps/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateApp(appId: number, data: Partial<App>): Promise<AuthResponse> {
    return this.request<AuthResponse>(`/api/apps/${appId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteApp(appId: number): Promise<AuthResponse> {
    return this.request<AuthResponse>(`/api/apps/${appId}`, {
      method: 'DELETE',
    });
  }

  // 试用申请相关API
  async sendTrialApplication(data: any): Promise<AuthResponse> {
    return this.request<AuthResponse>('/api/apps/trial-application', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

export const apiClient = new ApiClient();
export default apiClient;
